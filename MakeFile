# Loom Project Makefile
# Supports dev, test, staging, and production environments

.PHONY: help dev-start dev-stop test test-backend test-frontend lint lint-backend lint-frontend build build-backend build-frontend clean install install-backend install-frontend

# Default target
help:
	@echo "Loom Project Commands:"
	@echo ""
	@echo "Development:"
	@echo "  make dev-start          Start development environment"
	@echo "  make dev-stop           Stop development environment"
	@echo ""
	@echo "Testing:"
	@echo "  make test               Run all tests"
	@echo "  make test-backend       Run backend tests"
	@echo "  make test-frontend      Run frontend tests"
	@echo ""
	@echo "Linting:"
	@echo "  make lint               Run all linting"
	@echo "  make lint-backend       Run backend linting"
	@echo "  make lint-frontend      Run frontend linting"
	@echo ""
	@echo "Building:"
	@echo "  make build              Build all components"
	@echo "  make build-backend      Build backend"
	@echo "  make build-frontend     Build frontend"
	@echo ""
	@echo "Installation:"
	@echo "  make install            Install all dependencies"
	@echo "  make install-backend    Install backend dependencies"
	@echo "  make install-frontend   Install frontend dependencies"
	@echo ""
	@echo "Utilities:"
	@echo "  make clean              Clean build artifacts"

# Development commands
dev-start:
	@echo "🚀 Starting Loom development environment..."
	@if [ ! -f backend/.env ]; then \
		echo "📝 Creating backend .env from example..."; \
		cp backend/.env.example backend/.env; \
	fi
	@echo "🔧 Starting backend server..."
	cd backend && ./init.sh

dev-stop:
	@echo "🛑 Stopping development environment..."
	@pkill -f "uvicorn server.main:app" || true
	@echo "✅ Development environment stopped"

# Testing commands
test: test-backend test-frontend
	@echo "✅ All tests completed"

test-backend:
	@echo "🧪 Running backend tests..."
	cd backend && python -m pytest tests/ -v --cov=server --cov-report=term-missing

test-frontend:
	@echo "🧪 Running frontend tests..."
	@if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then \
		cd frontend && npm test; \
	else \
		echo "⚠️  Frontend not yet implemented"; \
	fi

# Linting commands
lint: lint-backend lint-frontend
	@echo "✅ All linting completed"

lint-backend:
	@echo "🔍 Running backend linting..."
	cd backend && python -m black server/ tests/ --check
	cd backend && python -m flake8 server/ tests/
	cd backend && python -m mypy server/

lint-frontend:
	@echo "🔍 Running frontend linting..."
	@if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then \
		cd frontend && npm run lint; \
	else \
		echo "⚠️  Frontend not yet implemented"; \
	fi

# Build commands
build: build-backend build-frontend
	@echo "✅ All builds completed"

build-backend:
	@echo "🏗️  Building backend..."
	cd backend && python -m pip install -r requirements.txt
	@echo "✅ Backend build completed"

build-frontend:
	@echo "🏗️  Building frontend..."
	@if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then \
		cd frontend && npm run build; \
	else \
		echo "⚠️  Frontend not yet implemented"; \
	fi

# Installation commands
install: install-backend install-frontend
	@echo "✅ All dependencies installed"

install-backend:
	@echo "📦 Installing backend dependencies..."
	cd backend && python -m pip install -r requirements.txt
	@echo "✅ Backend dependencies installed"

install-frontend:
	@echo "📦 Installing frontend dependencies..."
	@if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then \
		cd frontend && npm install; \
	else \
		echo "⚠️  Frontend not yet implemented"; \
	fi

# Utility commands
clean:
	@echo "🧹 Cleaning build artifacts..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	rm -rf backend/.coverage 2>/dev/null || true
	rm -rf backend/htmlcov 2>/dev/null || true
	@if [ -d "frontend/dist" ]; then rm -rf frontend/dist; fi
	@if [ -d "frontend/build" ]; then rm -rf frontend/build; fi
	@echo "✅ Cleanup completed"

# Format code (auto-fix)
format-backend:
	@echo "🎨 Formatting backend code..."
	cd backend && python -m black server/ tests/
	@echo "✅ Backend code formatted"

# Database commands (for future use)
db-migrate:
	@echo "🗄️  Running database migrations..."
	cd backend && alembic upgrade head

db-reset:
	@echo "🗄️  Resetting database..."
	cd backend && alembic downgrade base
	cd backend && alembic upgrade head