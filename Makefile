# Loom Project Makefile
# Supports dev, test, staging, and production environments

.PHONY: help dev-start dev-stop test test-backend test-frontend lint lint-backend lint-frontend build build-backend build-frontend clean install install-backend install-frontend

# Default target
help:
	@echo "Loom Project Commands:"
	@echo ""
	@echo "Development:"
	@echo "  make dev-start          Start development environment (Docker)"
	@echo "  make dev-stop           Stop development environment"
	@echo "  make dev-logs           Show development logs"
	@echo "  make dev-restart        Restart development environment"
	@echo ""
	@echo "Testing:"
	@echo "  make test               Run all tests"
	@echo "  make test-backend       Run backend tests"
	@echo "  make test-frontend      Run frontend tests"
	@echo ""
	@echo "Linting:"
	@echo "  make lint               Run all linting"
	@echo "  make lint-backend       Run backend linting"
	@echo "  make lint-frontend      Run frontend linting"
	@echo ""
	@echo "Building:"
	@echo "  make build              Build all components"
	@echo "  make build-backend      Build backend"
	@echo "  make build-frontend     Build frontend"
	@echo ""
	@echo "Installation:"
	@echo "  make install            Install all dependencies"
	@echo "  make install-backend    Install backend dependencies"
	@echo "  make install-frontend   Install frontend dependencies"
	@echo ""
	@echo "Utilities:"
	@echo "  make clean              Clean build artifacts"
	@echo ""
	@echo "Docker:"
	@echo "  make docker-build       Build all Docker images"
	@echo "  make docker-clean       Clean Docker resources"
	@echo "  make docker-shell-backend  Open shell in backend container"
	@echo "  make docker-shell-db    Open shell in database container"

# Development commands
dev-start:
	@if [ ! -f backend/.env ]; then \
		cp backend/.env.example backend/.env; \
	fi
	docker-compose up --build -d

dev-stop:
	docker-compose down

dev-logs:
	docker-compose logs -f

dev-restart:
	docker-compose restart

# Testing commands
test: test-backend test-frontend

test-backend:
	docker-compose exec backend python -m pytest tests/ -v

test-frontend:
	docker-compose exec frontend npm test

# Linting commands
lint: lint-backend lint-frontend

lint-backend:
	docker-compose exec backend python -m black server/ tests/ --check
	docker-compose exec backend python -m flake8 server/ tests/
	docker-compose exec backend python -m mypy server/

lint-frontend:
	docker-compose exec frontend npm run lint

# Build commands
build: build-backend build-frontend

build-backend:
	docker-compose build backend

build-frontend:
	docker-compose build frontend

# Installation commands
install: install-backend install-frontend

install-backend:
	docker-compose build backend

install-frontend:
	docker-compose build frontend

# Utility commands
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	rm -rf backend/.coverage 2>/dev/null || true
	rm -rf backend/htmlcov 2>/dev/null || true
	@if [ -d "frontend/dist" ]; then rm -rf frontend/dist; fi
	@if [ -d "frontend/build" ]; then rm -rf frontend/build; fi

# Format code (auto-fix)
format-backend:
	docker-compose exec backend python -m black server/ tests/

# Database commands
db-migrate:
	docker-compose exec backend alembic upgrade head

db-reset:
	docker-compose exec backend alembic downgrade base
	docker-compose exec backend alembic upgrade head

db-revision:
	docker-compose exec backend alembic revision --autogenerate -m "$(MESSAGE)"

db-current:
	docker-compose exec backend alembic current

db-history:
	docker-compose exec backend alembic history

db-show:
	docker-compose exec postgres psql -U loom_user -d loom -c "\dt"

# Development data seeding
seed-data:
	docker-compose exec backend python scripts/seed_dev_data.py

seed-data-reset:
	docker-compose exec backend python scripts/seed_dev_data.py --reset

# Docker-specific commands
docker-build:
	docker-compose build

docker-clean:
	docker-compose down -v --remove-orphans
	docker system prune -f

docker-shell-backend:
	docker-compose exec backend /bin/bash

docker-shell-frontend:
	docker-compose exec frontend /bin/sh

docker-shell-db:
	docker-compose exec postgres psql -U loom_user -d loom