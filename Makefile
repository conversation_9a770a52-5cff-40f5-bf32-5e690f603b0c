# Loom Project Makefile
# Supports dev, test, staging, and production environments

.PHONY: help dev-start dev-stop test test-backend test-frontend lint lint-backend lint-frontend build build-backend build-frontend clean install install-backend install-frontend

# Default target
help:
	@echo "Loom Project Commands:"
	@echo ""
	@echo "Development:"
	@echo "  make dev-start          Start development environment (Docker)"
	@echo "  make dev-stop           Stop development environment"
	@echo "  make dev-logs           Show development logs"
	@echo "  make dev-restart        Restart development environment"
	@echo ""
	@echo "Testing:"
	@echo "  make test               Run all tests"
	@echo "  make test-backend       Run backend tests"
	@echo "  make test-frontend      Run frontend tests"
	@echo ""
	@echo "Linting:"
	@echo "  make lint               Run all linting"
	@echo "  make lint-backend       Run backend linting"
	@echo "  make lint-frontend      Run frontend linting"
	@echo ""
	@echo "Building:"
	@echo "  make build              Build all components"
	@echo "  make build-backend      Build backend"
	@echo "  make build-frontend     Build frontend"
	@echo ""
	@echo "Installation:"
	@echo "  make install            Install all dependencies"
	@echo "  make install-backend    Install backend dependencies"
	@echo "  make install-frontend   Install frontend dependencies"
	@echo ""
	@echo "Utilities:"
	@echo "  make clean              Clean build artifacts"
	@echo ""
	@echo "Docker:"
	@echo "  make docker-build       Build all Docker images"
	@echo "  make docker-clean       Clean Docker resources"
	@echo "  make docker-shell-backend  Open shell in backend container"
	@echo "  make docker-shell-db    Open shell in database container"

# Development commands
dev-start:
	@echo "🚀 Starting Loom development environment..."
	@if [ ! -f backend/.env ]; then \
		echo "📝 Creating backend .env from example..."; \
		cp backend/.env.example backend/.env; \
	fi
	@echo "� Building and starting containers..."
	docker-compose up --build -d
	@echo "✅ Development environment started"
	@echo "🌐 Backend API: http://localhost:8000"
	@echo "📚 API Docs: http://localhost:8000/docs"

dev-stop:
	@echo "🛑 Stopping development environment..."
	docker-compose down
	@echo "✅ Development environment stopped"

dev-logs:
	@echo "📋 Showing development logs..."
	docker-compose logs -f

dev-restart:
	@echo "🔄 Restarting development environment..."
	docker-compose restart

# Testing commands
test: test-backend test-frontend
	@echo "✅ All tests completed"

test-backend:
	@echo "🧪 Running backend tests..."
	docker-compose exec backend python -m pytest tests/ -v

test-frontend:
	@echo "🧪 Running frontend tests..."
	@if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then \
		cd frontend && npm test; \
	else \
		echo "⚠️  Frontend not yet implemented"; \
	fi

# Linting commands
lint: lint-backend lint-frontend
	@echo "✅ All linting completed"

lint-backend:
	@echo "🔍 Running backend linting..."
	docker-compose exec backend python -m black server/ tests/ --check
	docker-compose exec backend python -m flake8 server/ tests/
	docker-compose exec backend python -m mypy server/

lint-frontend:
	@echo "🔍 Running frontend linting..."
	@if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then \
		cd frontend && npm run lint; \
	else \
		echo "⚠️  Frontend not yet implemented"; \
	fi

# Build commands
build: build-backend build-frontend
	@echo "✅ All builds completed"

build-backend:
	@echo "🏗️  Building backend..."
	docker-compose build backend
	@echo "✅ Backend build completed"

build-frontend:
	@echo "🏗️  Building frontend..."
	@if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then \
		cd frontend && npm run build; \
	else \
		echo "⚠️  Frontend not yet implemented"; \
	fi

# Installation commands
install: install-backend install-frontend
	@echo "✅ All dependencies installed"

install-backend:
	@echo "📦 Installing backend dependencies..."
	docker-compose build backend
	@echo "✅ Backend dependencies installed"

install-frontend:
	@echo "📦 Installing frontend dependencies..."
	@if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then \
		cd frontend && npm install; \
	else \
		echo "⚠️  Frontend not yet implemented"; \
	fi

# Utility commands
clean:
	@echo "🧹 Cleaning build artifacts..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	rm -rf backend/.coverage 2>/dev/null || true
	rm -rf backend/htmlcov 2>/dev/null || true
	@if [ -d "frontend/dist" ]; then rm -rf frontend/dist; fi
	@if [ -d "frontend/build" ]; then rm -rf frontend/build; fi
	@echo "✅ Cleanup completed"

# Format code (auto-fix)
format-backend:
	@echo "🎨 Formatting backend code..."
	docker-compose exec backend python -m black server/ tests/
	@echo "✅ Backend code formatted"

# Database commands (for future use)
db-migrate:
	@echo "🗄️  Running database migrations..."
	docker-compose exec backend alembic upgrade head

db-reset:
	@echo "🗄️  Resetting database..."
	docker-compose exec backend alembic downgrade base
	docker-compose exec backend alembic upgrade head

# Docker-specific commands
docker-build:
	@echo "🐳 Building all Docker images..."
	docker-compose build

docker-clean:
	@echo "🧹 Cleaning Docker resources..."
	docker-compose down -v --remove-orphans
	docker system prune -f
	@echo "✅ Docker cleanup completed"

docker-shell-backend:
	@echo "🐚 Opening shell in backend container..."
	docker-compose exec backend /bin/bash

docker-shell-db:
	@echo "🐚 Opening shell in database container..."
	docker-compose exec postgres psql -U loom_user -d loom