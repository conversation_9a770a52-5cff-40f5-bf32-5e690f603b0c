# Project Context

This file contains important context, patterns, conventions, and decisions about the codebase that agent based coding
tools can reference and utilize while working on the project.

## Overview

Loom is a application that allows users to explore large embedding datasets. The application allows users to upload 
datasets, view them in 3D space using a variety of dimensionality reduction algorithms and filter the points using a
variety of methods.

## Architecture Decisions

- This application consists of a postgres database, a FastAPI server and a React frontend.