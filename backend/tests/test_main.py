"""
Unit tests for the main FastAPI application.
"""

import pytest
from fastapi.testclient import TestClient


class TestMainEndpoints:
    """Test the main application endpoints."""
    
    def test_root_endpoint(self, client: TestClient):
        """Test the root endpoint returns correct information."""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["message"] == "Welcome to Loom API"
        assert data["version"] == "1.0.0"
        assert "description" in data
        assert data["docs"] == "/docs"
        assert data["health"] == "/health"
    
    def test_health_check_endpoint(self, client: TestClient):
        """Test the health check endpoint."""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["service"] == "loom-api"
        assert "environment" in data
    
    def test_api_status_endpoint(self, client: TestClient):
        """Test the API status endpoint."""
        response = client.get("/api/v1/status")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["api_version"] == "v1"
        assert data["status"] == "operational"
        assert "features" in data
        assert isinstance(data["features"], list)
        
        # Check that expected features are present
        expected_features = [
            "dataset_upload",
            "3d_visualization", 
            "dimensionality_reduction",
            "filtering"
        ]
        for feature in expected_features:
            assert feature in data["features"]
    
    def test_404_handler(self, client: TestClient):
        """Test custom 404 error handler."""
        response = client.get("/nonexistent-endpoint")
        
        assert response.status_code == 404
        data = response.json()
        
        assert data["error"] == "Not found"
        assert "message" in data
    
    def test_cors_headers(self, client: TestClient):
        """Test that CORS headers are properly set."""
        response = client.options("/", headers={"Origin": "http://localhost:3000"})
        
        # FastAPI's CORS middleware should handle this
        assert response.status_code in [200, 405]  # 405 is also acceptable for OPTIONS


class TestApplicationStructure:
    """Test application configuration and structure."""
    
    def test_app_metadata(self, client: TestClient):
        """Test that the app has correct metadata."""
        response = client.get("/docs")
        assert response.status_code == 200
        
        response = client.get("/redoc")
        assert response.status_code == 200
    
    def test_openapi_schema(self, client: TestClient):
        """Test that OpenAPI schema is available."""
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        schema = response.json()
        assert schema["info"]["title"] == "Loom API"
        assert schema["info"]["version"] == "1.0.0"
