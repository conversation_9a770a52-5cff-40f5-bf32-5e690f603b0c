"""
Pytest configuration and fixtures for Loom backend tests.
"""

import pytest
from fastapi.testclient import TestClient
from server.main import app
from server.config import Settings, get_settings


def get_test_settings():
    """Override settings for testing."""
    return Settings(
        environment="test",
        debug=True,
        database_url="sqlite:///./test.db",
        secret_key="test-secret-key",
        cors_origins="http://localhost:3000"
    )


@pytest.fixture
def test_settings():
    """Provide test settings."""
    return get_test_settings()


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    # Override the settings dependency for testing
    app.dependency_overrides[get_settings] = get_test_settings
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture
def sample_dataset():
    """Provide sample dataset for testing."""
    return {
        "name": "test_dataset",
        "description": "A test dataset for unit testing",
        "embeddings": [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6], [0.7, 0.8, 0.9]],
        "labels": ["item1", "item2", "item3"]
    }
