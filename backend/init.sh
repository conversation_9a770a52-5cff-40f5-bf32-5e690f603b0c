#!/bin/bash

set -e

# Check if we're in a container or local development
if [ -f /.dockerenv ]; then
    ENVIRONMENT="container"
else
    ENVIRONMENT="local"
fi

# Set default values
HOST=${HOST:-"0.0.0.0"}
PORT=${PORT:-8000}
WORKERS=${WORKERS:-1}
LOG_LEVEL=${LOG_LEVEL:-"info"}

# Install dependencies if requirements.txt is newer than last install
if [ ! -f .requirements_installed ] || [ requirements.txt -nt .requirements_installed ]; then
    pip install -r requirements.txt
    touch .requirements_installed 2>/dev/null || true
fi

# Run database migrations if needed
if [ -d "alembic" ] && [ -f "alembic.ini" ]; then
    alembic upgrade head
fi

# Seed development data if in development mode
if [ "$ENVIRONMENT" = "container" ] && [ "${SEED_DEV_DATA:-true}" = "true" ]; then
    python scripts/seed_dev_data.py
fi

if [ "$ENVIRONMENT" = "container" ]; then
    exec uvicorn server.main:app \
        --host "$HOST" \
        --port "$PORT" \
        --workers "$WORKERS" \
        --log-level "$LOG_LEVEL" \
        --access-log
else
    exec uvicorn server.main:app \
        --host "$HOST" \
        --port "$PORT" \
        --log-level "$LOG_LEVEL" \
        --reload \
        --reload-dir server
fi
