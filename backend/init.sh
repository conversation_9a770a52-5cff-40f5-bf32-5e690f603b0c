#!/bin/bash

# Backend initialization script for Loom application
# This script sets up and starts the FastAPI server

set -e  # Exit on any error

echo "🚀 Starting Loom Backend Server..."

# Check if we're in a container or local development
if [ -f /.dockerenv ]; then
    echo "📦 Running in container environment"
    ENVIRONMENT="container"
else
    echo "💻 Running in local development environment"
    ENVIRONMENT="local"
fi

# Set default values
HOST=${HOST:-"0.0.0.0"}
PORT=${PORT:-8000}
WORKERS=${WORKERS:-1}
LOG_LEVEL=${LOG_LEVEL:-"info"}

# Install dependencies if requirements.txt is newer than last install
if [ ! -f .requirements_installed ] || [ requirements.txt -nt .requirements_installed ]; then
    echo "📦 Installing Python dependencies..."
    pip install -r requirements.txt
    touch .requirements_installed
else
    echo "✅ Dependencies already up to date"
fi

# Run database migrations if needed
if [ -d "alembic" ] && [ -f "alembic.ini" ]; then
    echo "🗄️  Running database migrations..."
    alembic upgrade head
fi

# Start the FastAPI server
echo "🌐 Starting FastAPI server on ${HOST}:${PORT}"
echo "   Workers: ${WORKERS}"
echo "   Log Level: ${LOG_LEVEL}"
echo "   Environment: ${ENVIRONMENT}"

if [ "$ENVIRONMENT" = "container" ]; then
    # Production-like settings for container
    exec uvicorn server.main:app \
        --host "$HOST" \
        --port "$PORT" \
        --workers "$WORKERS" \
        --log-level "$LOG_LEVEL" \
        --access-log
else
    # Development settings with auto-reload
    exec uvicorn server.main:app \
        --host "$HOST" \
        --port "$PORT" \
        --log-level "$LOG_LEVEL" \
        --reload \
        --reload-dir server
fi
