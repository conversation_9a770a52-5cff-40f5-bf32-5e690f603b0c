APP_NAME="Loom API"
ENVIRONMENT=development
DEBUG=true

HOST=0.0.0.0
PORT=8000
WORKERS=1

DATABASE_URL=postgresql://loom_user:password@localhost:5432/loom
DB_HOST=localhost
DB_PORT=5432
DB_NAME=loom
DB_USER=loom_user
DB_PASSWORD=your_secure_password_here

SECRET_KEY=your-very-secure-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

CORS_ORIGINS=http://localhost:3000,http://localhost:5173

MAX_UPLOAD_SIZE=104857600
UPLOAD_DIR=uploads
