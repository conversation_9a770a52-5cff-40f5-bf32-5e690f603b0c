# Loom Backend Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Settings
APP_NAME="Loom API"
ENVIRONMENT=development  # dev, test, staging, production
DEBUG=true

# Server Settings
HOST=0.0.0.0
PORT=8000
WORKERS=1

# Database Settings
DATABASE_URL=postgresql://loom_user:password@localhost:5432/loom
DB_HOST=localhost
DB_PORT=5432
DB_NAME=loom
DB_USER=loom_user
DB_PASSWORD=your_secure_password_here

# Security Settings
SECRET_KEY=your-very-secure-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings (comma-separated list)
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# File Upload Settings
MAX_UPLOAD_SIZE=104857600  # 100MB in bytes
UPLOAD_DIR=uploads
