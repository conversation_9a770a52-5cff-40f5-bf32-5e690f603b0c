"""Convert timestamps to unix epoch milliseconds

Revision ID: 002
Revises: 001
Create Date: 2025-06-24 21:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
import time

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Convert existing datetime columns to integer unix timestamps (ms)
    
    # Users table
    op.add_column('users', sa.Column('created_at_new', sa.BigInteger(), nullable=True))
    op.add_column('users', sa.Column('updated_at_new', sa.BigInteger(), nullable=True))
    
    # Set default values for existing records
    current_time_ms = int(time.time() * 1000)
    op.execute(f"UPDATE users SET created_at_new = {current_time_ms}, updated_at_new = {current_time_ms}")
    
    # Drop old columns and rename new ones
    op.drop_column('users', 'created_at')
    op.drop_column('users', 'updated_at')
    op.alter_column('users', 'created_at_new', new_column_name='created_at', nullable=False)
    op.alter_column('users', 'updated_at_new', new_column_name='updated_at', nullable=False)
    
    # Datasets table
    op.add_column('datasets', sa.Column('created_at_new', sa.BigInteger(), nullable=True))
    op.add_column('datasets', sa.Column('updated_at_new', sa.BigInteger(), nullable=True))
    op.add_column('datasets', sa.Column('processed_at_new', sa.BigInteger(), nullable=True))
    
    # Set default values for existing records
    op.execute(f"UPDATE datasets SET created_at_new = {current_time_ms}, updated_at_new = {current_time_ms}")
    
    # Drop old columns and rename new ones
    op.drop_column('datasets', 'created_at')
    op.drop_column('datasets', 'updated_at')
    op.drop_column('datasets', 'processed_at')
    op.alter_column('datasets', 'created_at_new', new_column_name='created_at', nullable=False)
    op.alter_column('datasets', 'updated_at_new', new_column_name='updated_at', nullable=False)
    op.alter_column('datasets', 'processed_at_new', new_column_name='processed_at', nullable=True)
    
    # Datapoints table
    op.add_column('datapoints', sa.Column('created_at_new', sa.BigInteger(), nullable=True))
    op.add_column('datapoints', sa.Column('updated_at_new', sa.BigInteger(), nullable=True))
    
    # Set default values for existing records
    op.execute(f"UPDATE datapoints SET created_at_new = {current_time_ms}, updated_at_new = {current_time_ms}")
    
    # Drop old column and rename new ones
    op.drop_column('datapoints', 'created_at')
    op.alter_column('datapoints', 'created_at_new', new_column_name='created_at', nullable=False)
    op.alter_column('datapoints', 'updated_at_new', new_column_name='updated_at', nullable=False)
    
    # Embeddings table
    op.add_column('embeddings', sa.Column('created_at_new', sa.BigInteger(), nullable=True))
    op.add_column('embeddings', sa.Column('updated_at_new', sa.BigInteger(), nullable=True))
    
    # Set default values for existing records
    op.execute(f"UPDATE embeddings SET created_at_new = {current_time_ms}, updated_at_new = {current_time_ms}")
    
    # Drop old column and rename new ones
    op.drop_column('embeddings', 'created_at')
    op.alter_column('embeddings', 'created_at_new', new_column_name='created_at', nullable=False)
    op.alter_column('embeddings', 'updated_at_new', new_column_name='updated_at', nullable=False)


def downgrade() -> None:
    # Convert back to datetime columns
    
    # Users table
    op.add_column('users', sa.Column('created_at_old', sa.DateTime(timezone=True), nullable=True))
    op.add_column('users', sa.Column('updated_at_old', sa.DateTime(timezone=True), nullable=True))
    
    # Convert unix timestamps back to datetime (this is lossy for precision)
    op.execute("UPDATE users SET created_at_old = to_timestamp(created_at / 1000.0), updated_at_old = to_timestamp(updated_at / 1000.0)")
    
    op.drop_column('users', 'created_at')
    op.drop_column('users', 'updated_at')
    op.alter_column('users', 'created_at_old', new_column_name='created_at', nullable=False)
    op.alter_column('users', 'updated_at_old', new_column_name='updated_at', nullable=False)
    
    # Datasets table
    op.add_column('datasets', sa.Column('created_at_old', sa.DateTime(timezone=True), nullable=True))
    op.add_column('datasets', sa.Column('updated_at_old', sa.DateTime(timezone=True), nullable=True))
    op.add_column('datasets', sa.Column('processed_at_old', sa.DateTime(timezone=True), nullable=True))
    
    op.execute("UPDATE datasets SET created_at_old = to_timestamp(created_at / 1000.0), updated_at_old = to_timestamp(updated_at / 1000.0)")
    op.execute("UPDATE datasets SET processed_at_old = to_timestamp(processed_at / 1000.0) WHERE processed_at IS NOT NULL")
    
    op.drop_column('datasets', 'created_at')
    op.drop_column('datasets', 'updated_at')
    op.drop_column('datasets', 'processed_at')
    op.alter_column('datasets', 'created_at_old', new_column_name='created_at', nullable=False)
    op.alter_column('datasets', 'updated_at_old', new_column_name='updated_at', nullable=False)
    op.alter_column('datasets', 'processed_at_old', new_column_name='processed_at', nullable=True)
    
    # Datapoints table
    op.add_column('datapoints', sa.Column('created_at_old', sa.DateTime(timezone=True), nullable=True))
    
    op.execute("UPDATE datapoints SET created_at_old = to_timestamp(created_at / 1000.0)")
    
    op.drop_column('datapoints', 'created_at')
    op.drop_column('datapoints', 'updated_at')
    op.alter_column('datapoints', 'created_at_old', new_column_name='created_at', nullable=False)
    
    # Embeddings table
    op.add_column('embeddings', sa.Column('created_at_old', sa.DateTime(timezone=True), nullable=True))
    
    op.execute("UPDATE embeddings SET created_at_old = to_timestamp(created_at / 1000.0)")
    
    op.drop_column('embeddings', 'created_at')
    op.drop_column('embeddings', 'updated_at')
    op.alter_column('embeddings', 'created_at_old', new_column_name='created_at', nullable=False)
