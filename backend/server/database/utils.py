"""Database utility functions."""

import time
from datetime import datetime
from typing import Optional


def current_timestamp_ms() -> int:
    """Get current unix timestamp in milliseconds."""
    return int(time.time() * 1000)


def timestamp_to_datetime(timestamp_ms: int) -> datetime:
    """Convert unix timestamp in milliseconds to datetime object."""
    return datetime.fromtimestamp(timestamp_ms / 1000)


def datetime_to_timestamp(dt: datetime) -> int:
    """Convert datetime object to unix timestamp in milliseconds."""
    return int(dt.timestamp() * 1000)


def format_timestamp(timestamp_ms: Optional[int]) -> Optional[str]:
    """Format unix timestamp in milliseconds to human-readable string."""
    if timestamp_ms is None:
        return None
    return timestamp_to_datetime(timestamp_ms).strftime("%Y-%m-%d %H:%M:%S")


def parse_timestamp(timestamp_str: str) -> int:
    """Parse timestamp string to unix timestamp in milliseconds."""
    dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
    return datetime_to_timestamp(dt)
