"""Database service functions for common operations."""

from sqlalchemy.orm import Session
from typing import List, Optional
from .tables import User, Dataset, DataPoint, Embedding


class UserService:
    """Service class for user operations."""
    
    @staticmethod
    def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
        """Get user by ID."""
        return db.query(User).filter(User.id == user_id).first()
    
    @staticmethod
    def get_user_by_username(db: Session, username: str) -> Optional[User]:
        """Get user by username."""
        return db.query(User).filter(User.username == username).first()
    
    @staticmethod
    def get_user_by_email(db: Session, email: str) -> Optional[User]:
        """Get user by email."""
        return db.query(User).filter(User.email == email).first()
    
    @staticmethod
    def create_user(db: Session, username: str, email: str, hashed_password: str, 
                   full_name: Optional[str] = None) -> User:
        """Create a new user."""
        user = User(
            username=username,
            email=email,
            hashed_password=hashed_password,
            full_name=full_name
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user


class DatasetService:
    """Service class for dataset operations."""
    
    @staticmethod
    def get_dataset_by_id(db: Session, dataset_id: int) -> Optional[Dataset]:
        """Get dataset by ID."""
        return db.query(Dataset).filter(Dataset.id == dataset_id).first()
    
    @staticmethod
    def get_datasets_by_user(db: Session, user_id: int) -> List[Dataset]:
        """Get all datasets owned by a user."""
        return db.query(Dataset).filter(Dataset.owner_id == user_id).all()
    
    @staticmethod
    def create_dataset(db: Session, name: str, owner_id: int, 
                      description: Optional[str] = None) -> Dataset:
        """Create a new dataset."""
        dataset = Dataset(
            name=name,
            description=description,
            owner_id=owner_id
        )
        db.add(dataset)
        db.commit()
        db.refresh(dataset)
        return dataset


class DataPointService:
    """Service class for datapoint operations."""
    
    @staticmethod
    def get_datapoints_by_dataset(db: Session, dataset_id: int) -> List[DataPoint]:
        """Get all datapoints for a dataset."""
        return db.query(DataPoint).filter(DataPoint.dataset_id == dataset_id).all()
    
    @staticmethod
    def create_datapoint(db: Session, dataset_id: int, label: Optional[str] = None,
                        data_metadata: Optional[dict] = None, 
                        original_index: Optional[int] = None) -> DataPoint:
        """Create a new datapoint."""
        datapoint = DataPoint(
            dataset_id=dataset_id,
            label=label,
            data_metadata=data_metadata,
            original_index=original_index
        )
        db.add(datapoint)
        db.commit()
        db.refresh(datapoint)
        return datapoint


class EmbeddingService:
    """Service class for embedding operations."""
    
    @staticmethod
    def get_embeddings_by_datapoint(db: Session, datapoint_id: int) -> List[Embedding]:
        """Get all embeddings for a datapoint."""
        return db.query(Embedding).filter(Embedding.datapoint_id == datapoint_id).all()
    
    @staticmethod
    def get_embeddings_by_type(db: Session, dataset_id: int, 
                              embedding_type: str) -> List[Embedding]:
        """Get embeddings by type for a dataset."""
        return db.query(Embedding).join(DataPoint).filter(
            DataPoint.dataset_id == dataset_id,
            Embedding.embedding_type == embedding_type
        ).all()
    
    @staticmethod
    def create_embedding(db: Session, datapoint_id: int, embedding_type: str,
                        dimensions: int, vector: List[float], 
                        method_params: Optional[dict] = None,
                        x: Optional[float] = None, y: Optional[float] = None, 
                        z: Optional[float] = None) -> Embedding:
        """Create a new embedding."""
        embedding = Embedding(
            datapoint_id=datapoint_id,
            embedding_type=embedding_type,
            dimensions=dimensions,
            vector=vector,
            method_params=method_params,
            x=x, y=y, z=z
        )
        db.add(embedding)
        db.commit()
        db.refresh(embedding)
        return embedding
