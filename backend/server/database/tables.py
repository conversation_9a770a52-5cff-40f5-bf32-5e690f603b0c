"""SQLAlchemy table definitions for Loom application."""

import time
from sqlalchemy import <PERSON>umn, Integer, BigInteger, String, Text, Float, ForeignKey, Boolean, JSON
from sqlalchemy.orm import relationship
from .connection import Base


class BaseModel(Base):
    """Base model with common timestamp fields."""
    __abstract__ = True

    created_at = Column(BigInteger, nullable=False, default=lambda: int(time.time() * 1000))
    updated_at = Column(BigInteger, nullable=False, default=lambda: int(time.time() * 1000), onupdate=lambda: int(time.time() * 1000))


class User(BaseModel):
    """User table for authentication and user management."""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)

    # Relationships
    datasets = relationship("Dataset", back_populates="owner", cascade="all, delete-orphan")


class Dataset(BaseModel):
    """Dataset table for storing embedding dataset metadata."""
    __tablename__ = "datasets"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Dataset metadata
    total_points = Column(Integer, default=0, nullable=False)
    embedding_dimension = Column(Integer, nullable=True)
    file_path = Column(String(500), nullable=True)
    file_size = Column(Integer, nullable=True)  # Size in bytes

    # Processing status
    status = Column(String(50), default="uploaded", nullable=False)  # uploaded, processing, ready, error
    error_message = Column(Text, nullable=True)
    processed_at = Column(BigInteger, nullable=True)  # Unix timestamp in ms

    # Relationships
    owner = relationship("User", back_populates="datasets")
    datapoints = relationship("DataPoint", back_populates="dataset", cascade="all, delete-orphan")


class DataPoint(BaseModel):
    """DataPoint table for individual data points in a dataset."""
    __tablename__ = "datapoints"

    id = Column(Integer, primary_key=True, index=True)
    dataset_id = Column(Integer, ForeignKey("datasets.id"), nullable=False)

    # Data point metadata
    label = Column(String(500), nullable=True)  # Human-readable label
    data_metadata = Column(JSON, nullable=True)  # Additional metadata as JSON

    # Original data reference
    original_index = Column(Integer, nullable=True)  # Index in original dataset

    # Relationships
    dataset = relationship("Dataset", back_populates="datapoints")
    embeddings = relationship("Embedding", back_populates="datapoint", cascade="all, delete-orphan")


class Embedding(BaseModel):
    """Embedding table for storing vector embeddings and reduced dimensions."""
    __tablename__ = "embeddings"

    id = Column(Integer, primary_key=True, index=True)
    datapoint_id = Column(Integer, ForeignKey("datapoints.id"), nullable=False)

    # Embedding type and method
    embedding_type = Column(String(50), nullable=False)  # original, umap, tsne, pca, etc.
    method_params = Column(JSON, nullable=True)  # Parameters used for dimensionality reduction

    # Vector data
    dimensions = Column(Integer, nullable=False)  # Number of dimensions
    vector = Column(JSON, nullable=False)  # The actual embedding vector as JSON array

    # 3D coordinates for visualization (if applicable)
    x = Column(Float, nullable=True)
    y = Column(Float, nullable=True)
    z = Column(Float, nullable=True)

    # Relationships
    datapoint = relationship("DataPoint", back_populates="embeddings")
