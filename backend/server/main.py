"""
Loom FastAPI Application Entry Point

This is the main FastAPI application for the Loom embedding dataset explorer.
"""

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
from typing import Dict, Any

# Create FastAPI application instance
app = FastAPI(
    title="Loom API",
    description="API for exploring large embedding datasets in 3D space",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root() -> Dict[str, Any]:
    """Root endpoint providing basic API information."""
    return {
        "message": "Welcome to Loom API",
        "version": "1.0.0",
        "description": "API for exploring large embedding datasets",
        "docs": "/docs",
        "health": "/health"
    }


@app.get("/health")
async def health_check() -> Dict[str, Any]:
    """Health check endpoint for monitoring and container orchestration."""
    return {
        "status": "healthy",
        "service": "loom-api",
        "environment": os.getenv("ENVIRONMENT", "development")
    }


@app.get("/api/v1/status")
async def api_status() -> Dict[str, Any]:
    """API status endpoint providing service information."""
    return {
        "api_version": "v1",
        "status": "operational",
        "features": [
            "dataset_upload",
            "3d_visualization", 
            "dimensionality_reduction",
            "filtering"
        ]
    }


# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    """Custom 404 handler."""
    return JSONResponse(
        status_code=404,
        content={"error": "Not found", "message": "The requested resource was not found"}
    )


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    """Custom 500 handler."""
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "message": "An unexpected error occurred"}
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
