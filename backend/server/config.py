"""
Configuration settings for the Loom application.

This module handles environment-specific configuration for dev, test, staging, and production modes.
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application settings
    app_name: str = Field(default="Loom API", description="Application name")
    environment: str = Field(default="development", description="Environment mode")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Server settings
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8000, description="Server port")
    workers: int = Field(default=1, description="Number of worker processes")
    
    # Database settings
    database_url: Optional[str] = Field(default=None, description="Database connection URL")
    db_host: str = Field(default="localhost", description="Database host")
    db_port: int = Field(default=5432, description="Database port")
    db_name: str = Field(default="loom", description="Database name")
    db_user: str = Field(default="loom_user", description="Database user")
    db_password: str = Field(default="", description="Database password")
    
    # Security settings
    secret_key: str = Field(default="your-secret-key-change-in-production", description="Secret key for JWT")
    algorithm: str = Field(default="HS256", description="JWT algorithm")
    access_token_expire_minutes: int = Field(default=30, description="Access token expiration time")
    
    # CORS settings
    cors_origins: str = Field(default="*", description="Allowed CORS origins (comma-separated)")
    
    # File upload settings
    max_upload_size: int = Field(default=100 * 1024 * 1024, description="Max upload size in bytes (100MB)")
    upload_dir: str = Field(default="uploads", description="Upload directory")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @property
    def database_url_complete(self) -> str:
        """Construct complete database URL if not provided directly."""
        if self.database_url:
            return self.database_url
        
        return f"postgresql://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_name}"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.environment.lower() in ["dev", "development"]
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self.environment.lower() in ["prod", "production"]
    
    @property
    def is_testing(self) -> bool:
        """Check if running in test mode."""
        return self.environment.lower() in ["test", "testing"]

    @property
    def cors_origins_list(self) -> list[str]:
        """Parse CORS origins from comma-separated string."""
        if self.cors_origins == "*":
            return ["*"]
        return [origin.strip() for origin in self.cors_origins.split(",") if origin.strip()]


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings instance."""
    return settings
