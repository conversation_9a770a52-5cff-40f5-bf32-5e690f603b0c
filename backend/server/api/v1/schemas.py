"""Pydantic schemas for API responses."""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime


class DatasetResponse(BaseModel):
    """Dataset response schema."""
    id: int
    name: str
    description: Optional[str] = None
    owner_id: int
    total_points: int
    embedding_dimension: Optional[int] = None
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    status: str
    error_message: Optional[str] = None
    created_at: int
    updated_at: int
    processed_at: Optional[int] = None

    class Config:
        from_attributes = True


class DatasetListResponse(BaseModel):
    """Response for listing datasets."""
    datasets: List[DatasetResponse]
    total: int
    skip: int
    limit: int


class EmbeddingPoint3D(BaseModel):
    """3D embedding point for visualization."""
    id: int
    x: float
    y: float
    z: float
    label: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    embedding_type: str
    dimensions: int
    original_index: Optional[int] = None


class EmbeddingResponse(BaseModel):
    """Response for embedding data."""
    dataset_id: int
    embedding_type: Optional[str] = None
    total_points: int
    embeddings: List[EmbeddingPoint3D]


class DataPointResponse(BaseModel):
    """DataPoint response schema."""
    id: int
    dataset_id: int
    label: Optional[str] = None
    data_metadata: Optional[Dict[str, Any]] = None
    original_index: Optional[int] = None
    created_at: int
    updated_at: int

    class Config:
        from_attributes = True


class UserResponse(BaseModel):
    """User response schema (excluding sensitive data)."""
    id: int
    username: str
    email: str
    full_name: Optional[str] = None
    is_active: bool
    created_at: int
    updated_at: int

    class Config:
        from_attributes = True


class ErrorResponse(BaseModel):
    """Standard error response."""
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None


class HealthResponse(BaseModel):
    """Health check response."""
    status: str
    service: str
    environment: str
    timestamp: int
    database_connected: bool = True


class APIStatusResponse(BaseModel):
    """API status response."""
    api_version: str
    status: str
    features: List[str]
    uptime_seconds: Optional[float] = None
