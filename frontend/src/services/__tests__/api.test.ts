/**
 * Tests for API service
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import ApiService from '../api';
import axios from 'axios';
import {
  mockDataset,
  mockEmbeddingPoint,
  mockDatasetInfo
} from '../../test/utils';

// Mock axios
vi.mock('axios');
const mockAxios = vi.mocked(axios);

// Create a mock axios instance
const mockAxiosInstance = {
  get: vi.fn(),
  interceptors: {
    response: {
      use: vi.fn()
    }
  }
};

mockAxios.create = vi.fn(() => mockAxiosInstance as any);

describe('ApiService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('healthCheck', () => {
    it('should return health status on success', async () => {
      const mockResponse = { status: 'healthy', timestamp: Date.now() };
      mockAxiosInstance.get.mockResolvedValueOnce({
        data: mockResponse
      });

      const result = await ApiService.healthCheck();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/health');
      expect(result).toEqual(mockResponse);
    });

    it('should handle network errors', async () => {
      mockAxiosInstance.get.mockRejectedValueOnce(new Error('Network error'));

      await expect(ApiService.healthCheck()).rejects.toThrow('Network error');
    });
  });

  describe('getDatasets', () => {
    it('should return datasets list on success', async () => {
      const mockResponse = {
        datasets: [mockDataset],
        total: 1,
        page: 1,
        per_page: 10
      };

      mockAxiosInstance.get.mockResolvedValueOnce({
        data: mockResponse
      });

      const result = await ApiService.getDatasets();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/datasets/', {
        params: { skip: 0, limit: 100 }
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty datasets list', async () => {
      const mockResponse = {
        datasets: [],
        total: 0,
        page: 1,
        per_page: 10
      };

      mockAxiosInstance.get.mockResolvedValueOnce({
        data: mockResponse
      });

      const result = await ApiService.getDatasets();

      expect(result.datasets).toHaveLength(0);
      expect(result.total).toBe(0);
    });
  });

  describe('getDataset', () => {
    it('should return specific dataset on success', async () => {
      mockAxiosInstance.get.mockResolvedValueOnce({
        data: mockDataset
      });

      const result = await ApiService.getDataset(1);

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/datasets/1');
      expect(result).toEqual(mockDataset);
    });
  });

});
