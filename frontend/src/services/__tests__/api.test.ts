/**
 * Tests for API service
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  mockDataset,
  mockEmbeddingPoint,
  mockDatasetInfo
} from '../../test/utils';

// Mock axios completely
vi.mock('axios', () => {
  const mockAxiosInstance = {
    get: vi.fn(),
    interceptors: {
      response: {
        use: vi.fn()
      }
    }
  };

  return {
    default: {
      create: vi.fn(() => mockAxiosInstance),
      ...mockAxiosInstance
    }
  };
});

// Import after mocking
const ApiService = await import('../api');
const axios = await import('axios');
const mockAxiosInstance = (axios.default.create as any)();

describe('ApiService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('healthCheck', () => {
    it('should return true on successful API connection', async () => {
      const mockResponse = { datasets: [], total: 0 };
      mockAxiosInstance.get.mockResolvedValueOnce({
        data: mockResponse
      });

      const result = await ApiService.default.healthCheck();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/datasets/', {
        params: { limit: 1 }
      });
      expect(result).toBe(true);
    });

    it('should return false on network errors', async () => {
      mockAxiosInstance.get.mockRejectedValueOnce(new Error('Network error'));

      const result = await ApiService.default.healthCheck();
      expect(result).toBe(false);
    });
  });

  describe('getDatasets', () => {
    it('should return datasets list on success', async () => {
      const mockResponse = {
        datasets: [mockDataset],
        total: 1,
        page: 1,
        per_page: 10
      };

      mockAxiosInstance.get.mockResolvedValueOnce({
        data: mockResponse
      });

      const result = await ApiService.default.getDatasets();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/datasets/', {
        params: { skip: 0, limit: 100 }
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty datasets list', async () => {
      const mockResponse = {
        datasets: [],
        total: 0,
        page: 1,
        per_page: 10
      };

      mockAxiosInstance.get.mockResolvedValueOnce({
        data: mockResponse
      });

      const result = await ApiService.default.getDatasets();

      expect(result.datasets).toHaveLength(0);
      expect(result.total).toBe(0);
    });
  });

  describe('getDataset', () => {
    it('should return specific dataset on success', async () => {
      mockAxiosInstance.get.mockResolvedValueOnce({
        data: mockDataset
      });

      const result = await ApiService.default.getDataset(1);

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/datasets/1');
      expect(result).toEqual(mockDataset);
    });
  });

});
