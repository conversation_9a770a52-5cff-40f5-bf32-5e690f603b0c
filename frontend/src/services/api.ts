/**
 * API service for communicating with the Loom backend
 */

import axios from 'axios';
import {
  Dataset,
  DatasetListResponse,
  EmbeddingPoint3D,
  EmbeddingResponse,
  DatasetInfoResponse,
  ApiError
} from '../types/api';

// Configure axios with base URL
const api = axios.create({
  baseURL: 'http://localhost:8000/api/v1',
  timeout: 30000, // 30 seconds for large datasets
});

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.data) {
      throw new Error(error.response.data.detail || error.response.data.message || 'API Error');
    }
    throw new Error(error.message || 'Network Error');
  }
);

export class ApiService {
  /**
   * Get list of all datasets
   */
  static async getDatasets(skip = 0, limit = 100): Promise<DatasetListResponse> {
    const response = await api.get<DatasetListResponse>('/datasets/', {
      params: { skip, limit }
    });
    return response.data;
  }

  /**
   * Get a specific dataset by ID
   */
  static async getDataset(datasetId: number): Promise<Dataset> {
    const response = await api.get<Dataset>(`/datasets/${datasetId}`);
    return response.data;
  }

  /**
   * Get 3D embeddings for visualization with sampling support
   */
  static async getEmbeddings3D(
    datasetId: number,
    options: {
      embeddingType?: string;
      sampleSize?: number;
      samplingMethod?: 'random' | 'systematic' | 'first_n';
      seed?: number;
    } = {}
  ): Promise<EmbeddingPoint3D[]> {
    const {
      embeddingType = 'umap_3d',
      sampleSize = 100000,
      samplingMethod = 'random',
      seed
    } = options;

    const params: any = {
      embedding_type: embeddingType,
      sample_size: sampleSize,
      sampling_method: samplingMethod
    };

    if (seed !== undefined) {
      params.seed = seed;
    }

    const response = await api.get<EmbeddingPoint3D[]>(
      `/datasets/${datasetId}/embeddings/3d`,
      { params }
    );
    return response.data;
  }

  /**
   * Get all embeddings for a dataset
   */
  static async getAllEmbeddings(
    datasetId: number,
    embeddingType?: string,
    limit?: number
  ): Promise<EmbeddingResponse> {
    const params: any = {};
    if (embeddingType) {
      params.embedding_type = embeddingType;
    }
    if (limit) {
      params.limit = limit;
    }

    const response = await api.get<EmbeddingResponse>(
      `/datasets/${datasetId}/embeddings`,
      { params }
    );
    return response.data;
  }

  /**
   * Get dataset info with statistics
   */
  static async getDatasetInfo(datasetId: number): Promise<DatasetInfoResponse> {
    const response = await api.get<DatasetInfoResponse>(`/datasets/${datasetId}/info`);
    return response.data;
  }

  /**
   * Test API connectivity
   */
  static async healthCheck(): Promise<boolean> {
    try {
      await api.get('/datasets/', { params: { limit: 1 } });
      return true;
    } catch (error) {
      console.error('API health check failed:', error);
      return false;
    }
  }
}

export default ApiService;
