import React, { useState, useEffect } from 'react'
import axios from 'axios'

function App() {
  const [apiStatus, setApiStatus] = useState(null)
  const [health, setHealth] = useState(null)

  useEffect(() => {
    // Test API connectivity
    const fetchData = async () => {
      try {
        const [statusRes, healthRes] = await Promise.all([
          axios.get('/api/v1/status'),
          axios.get('/health')
        ])
        setApiStatus(statusRes.data)
        setHealth(healthRes.data)
      } catch (error) {
        console.error('API Error:', error)
      }
    }

    fetchData()
  }, [])

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Loom - Embedding Dataset Explorer</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>Backend Status</h2>
        {health ? (
          <div style={{ background: '#e8f5e8', padding: '10px', borderRadius: '4px' }}>
            <p>Status: {health.status}</p>
            <p>Service: {health.service}</p>
            <p>Environment: {health.environment}</p>
          </div>
        ) : (
          <p>Loading health status...</p>
        )}
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>API Status</h2>
        {apiStatus ? (
          <div style={{ background: '#e8f5e8', padding: '10px', borderRadius: '4px' }}>
            <p>API Version: {apiStatus.api_version}</p>
            <p>Status: {apiStatus.status}</p>
            <p>Features: {apiStatus.features.join(', ')}</p>
          </div>
        ) : (
          <p>Loading API status...</p>
        )}
      </div>

      <div>
        <h2>Next Steps</h2>
        <ul>
          <li>Upload embedding datasets</li>
          <li>Visualize in 3D space</li>
          <li>Apply dimensionality reduction</li>
          <li>Filter and explore data</li>
        </ul>
      </div>
    </div>
  )
}

export default App
