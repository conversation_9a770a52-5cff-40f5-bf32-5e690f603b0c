/**
 * Testing utilities and helpers
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { vi } from 'vitest';

// Mock data for testing
export const mockDataset = {
  id: 1,
  name: 'Test Dataset',
  description: 'A test dataset for unit tests',
  owner_id: 1,
  total_points: 1000,
  embedding_dimension: 512,
  file_path: '/test/path',
  file_size: 1024000,
  status: 'ready',
  created_at: Date.now(),
  updated_at: Date.now(),
  processed_at: Date.now()
};

export const mockEmbeddingPoint = {
  id: 1,
  x: 1.5,
  y: 2.3,
  z: -0.8,
  label: 'Test Point',
  metadata: {
    creature_type: 'Sparkly_Unicorn',
    rarity: 'legendary',
    magic_level: 8,
    habitat: 'enchanted_forest'
  },
  embedding_type: 'umap_3d',
  dimensions: 3,
  original_index: 0
};

export const mockDatasetInfo = {
  dataset: mockDataset,
  embedding_types: [
    {
      type: 'umap_3d',
      count: 1000,
      bounds: {
        x: [-5.0, 5.0],
        y: [-3.0, 3.0],
        z: [-2.0, 2.0]
      }
    },
    {
      type: 'original',
      count: 1000,
      bounds: {}
    }
  ]
};

export const mockVisualizationConfig = {
  pointSize: 0.17, // Equivalent to old size 5 (5/30 ≈ 0.17)
  colorBy: 'creature_type' as const,
  showLabels: false,
  embeddingType: 'umap_3d'
};

// Custom render function with providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };

// Mock API service
export const mockApiService = {
  getDatasets: vi.fn(),
  getDataset: vi.fn(),
  getEmbeddings3D: vi.fn(),
  getAllEmbeddings: vi.fn(),
  getDatasetInfo: vi.fn(),
  healthCheck: vi.fn()
};

// Helper to create mock API responses
export const createMockApiResponse = <T,>(data: T): Promise<T> => {
  return Promise.resolve(data);
};

export const createMockApiError = (message: string): Promise<never> => {
  return Promise.reject(new Error(message));
};

// Helper to wait for async operations
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0));

// Mock Deck.gl components
export const mockDeckGL = {
  ScatterplotLayer: vi.fn(() => ({})),
  OrthographicView: vi.fn(() => ({}))
};

// Helper to mock window.ResizeObserver
export const mockResizeObserver = () => {
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));
};
