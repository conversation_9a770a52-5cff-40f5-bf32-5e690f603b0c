/**
 * Color mapping utilities for visualization
 */

import { ColorMapping } from '../types/api';

export const CREATURE_COLORS: ColorMapping = {
  'Sparkly_Unicorn': [255, 192, 203],    // Pink
  'Dancing_Dragon': [255, 69, 0],        // Red-Orange
  'Mystical_Phoenix': [255, 140, 0],     // Dark Orange
  'Giggling_Goblin': [50, 205, 50],      // <PERSON>e Green
  'Wise_Owl': [139, 69, 19],             // Saddle Brown
  'Bouncing_Butterfly': [255, 20, 147],  // Deep Pink
  'Sleepy_Bear': [139, 90, 43],          // Saddle Brown
  'Curious_Cat': [128, 0, 128],          // Purple
  'Majestic_Eagle': [25, 25, 112],       // Midnight Blue
  'Playful_Dolphin': [0, 191, 255],      // Deep Sky Blue
  'default': [128, 128, 128]             // Gray
};

export const RARITY_COLORS: ColorMapping = {
  'common': [169, 169, 169],     // Dark Gray
  'uncommon': [50, 205, 50],     // Lime Green
  'rare': [0, 191, 255],         // Deep Sky Blue
  'legendary': [255, 215, 0],    // <PERSON>
  'default': [128, 128, 128]     // Gray
};

export type ColorScheme = 'creature_type' | 'rarity' | 'magic_level' | 'default';

/**
 * Get color for a point based on the specified color scheme
 */
export function getPointColor(
  metadata: Record<string, any>,
  colorScheme: ColorScheme
): [number, number, number] {
  switch (colorScheme) {
    case 'creature_type':
      const creatureType = metadata?.creature_type || 'default';
      return CREATURE_COLORS[creatureType] || CREATURE_COLORS.default;
    
    case 'rarity':
      const rarity = metadata?.rarity || 'default';
      return RARITY_COLORS[rarity] || RARITY_COLORS.default;
    
    case 'magic_level':
      const magicLevel = metadata?.magic_level || 5;
      const intensity = Math.min(255, (magicLevel / 10) * 255);
      return [intensity, 0, 255 - intensity]; // Blue to Red gradient
    
    default:
      return [100, 150, 255]; // Default blue
  }
}

/**
 * Get all available color schemes
 */
export function getAvailableColorSchemes(): Array<{value: ColorScheme, label: string}> {
  return [
    { value: 'creature_type', label: 'Creature Type' },
    { value: 'rarity', label: 'Rarity' },
    { value: 'magic_level', label: 'Magic Level' },
    { value: 'default', label: 'Default' }
  ];
}
