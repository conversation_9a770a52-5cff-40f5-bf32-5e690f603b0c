/**
 * Utility functions for formatting data
 */

/**
 * Format a timestamp to a readable date string
 */
export function formatDate(timestamp: number): string {
  return new Date(timestamp).toLocaleDateString();
}

/**
 * Format file size in bytes to human readable format
 */
export function formatFileSize(bytes?: number): string {
  if (bytes === undefined || bytes === null) return 'N/A';

  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];

  if (bytes === 0) return '0 B';

  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  if (i === 0) return `${bytes} ${sizes[i]}`;

  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
}

/**
 * Format a number with thousands separators
 */
export function formatNumber(num: number): string {
  return num.toLocaleString();
}

/**
 * Format coordinates to a fixed number of decimal places
 */
export function formatCoordinate(coord: number, decimals: number = 2): string {
  return coord.toFixed(decimals);
}

/**
 * Capitalize the first letter of a string and replace underscores with spaces
 */
export function formatLabel(str: string): string {
  return str
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
}
