/**
 * Tests for viewport utilities
 */

import { describe, it, expect } from 'vitest';
import { 
  calculateOptimalViewport, 
  validateViewportState, 
  createDefaultViewport 
} from '../viewport';
import { EmbeddingTypeStats, ViewportState } from '../../types/api';

describe('viewport utilities', () => {
  describe('calculateOptimalViewport', () => {
    it('should calculate viewport for valid bounds', () => {
      const embeddingStats: EmbeddingTypeStats = {
        type: 'umap_3d',
        count: 1000,
        bounds: {
          x: [-5, 5],
          y: [-3, 3]
        }
      };

      const viewport = calculateOptimalViewport(embeddingStats);

      expect(viewport.target).toEqual([0, 0, 0]); // Center X, Y, Z
      expect(viewport.rotationOrbit).toBe(0);
      expect(viewport.rotationX).toBe(30);
      expect(typeof viewport.zoom).toBe('number');
    });

    it('should handle asymmetric bounds', () => {
      const embeddingStats: EmbeddingTypeStats = {
        type: 'umap_3d',
        count: 500,
        bounds: {
          x: [-10, 2],
          y: [1, 8]
        }
      };

      const viewport = calculateOptimalViewport(embeddingStats);

      expect(viewport.target).toEqual([-4, 4.5, 0]); // (-10 + 2) / 2, (1 + 8) / 2, 0
    });

    it('should return default viewport when bounds are missing', () => {
      const embeddingStats: EmbeddingTypeStats = {
        type: 'umap_3d',
        count: 100,
        bounds: {}
      };

      const viewport = calculateOptimalViewport(embeddingStats);

      expect(viewport.target).toEqual([0, 0, 0]);
      expect(viewport.rotationOrbit).toBe(0);
      expect(viewport.rotationX).toBe(30);
      expect(viewport.zoom).toBe(0);
    });

    it('should return default viewport when x bounds are missing', () => {
      const embeddingStats: EmbeddingTypeStats = {
        type: 'umap_3d',
        count: 100,
        bounds: {
          y: [-1, 1]
        }
      };

      const viewport = calculateOptimalViewport(embeddingStats);

      expect(viewport.target).toEqual([0, 0, 0]);
      expect(viewport.rotationOrbit).toBe(0);
      expect(viewport.rotationX).toBe(30);
      expect(viewport.zoom).toBe(0);
    });

    it('should calculate zoom based on data range', () => {
      const smallRange: EmbeddingTypeStats = {
        type: 'umap_3d',
        count: 100,
        bounds: {
          x: [-1, 1],
          y: [-1, 1]
        }
      };

      const largeRange: EmbeddingTypeStats = {
        type: 'umap_3d',
        count: 100,
        bounds: {
          x: [-50, 50],
          y: [-50, 50]
        }
      };

      const smallViewport = calculateOptimalViewport(smallRange);
      const largeViewport = calculateOptimalViewport(largeRange);

      // Smaller range should have higher zoom
      expect(smallViewport.zoom!).toBeGreaterThan(largeViewport.zoom!);
    });

    it('should use larger of x or y range for zoom calculation', () => {
      const wideRange: EmbeddingTypeStats = {
        type: 'umap_3d',
        count: 100,
        bounds: {
          x: [-10, 10], // Range of 20
          y: [-1, 1]    // Range of 2
        }
      };

      const tallRange: EmbeddingTypeStats = {
        type: 'umap_3d',
        count: 100,
        bounds: {
          x: [-1, 1],   // Range of 2
          y: [-10, 10]  // Range of 20
        }
      };

      const wideViewport = calculateOptimalViewport(wideRange);
      const tallViewport = calculateOptimalViewport(tallRange);

      // Both should have similar zoom since they use the larger range
      expect(Math.abs(wideViewport.zoom! - tallViewport.zoom!)).toBeLessThan(0.1);
    });
  });

  describe('validateViewportState', () => {
    it('should validate correct viewport state', () => {
      const validViewport: ViewportState = {
        target: [0, 0, 0],
        rotationOrbit: 0,
        rotationX: 30,
        zoom: 1
      };

      expect(validateViewportState(validViewport)).toBe(true);
    });

    it('should reject viewport with NaN values', () => {
      const invalidViewport: ViewportState = {
        target: [NaN, 0, 0],
        rotationOrbit: 0,
        rotationX: 30,
        zoom: 1
      };

      expect(validateViewportState(invalidViewport)).toBe(false);
    });

    it('should reject viewport with non-number values', () => {
      const invalidViewport = {
        target: ['0', 0, 0],
        rotationOrbit: 0,
        rotationX: 30,
        zoom: 1
      } as any;

      expect(validateViewportState(invalidViewport)).toBe(false);
    });

    it('should validate viewport with negative values', () => {
      const validViewport: ViewportState = {
        target: [-180, -90, -10],
        rotationOrbit: -180,
        rotationX: -45,
        zoom: -5
      };

      expect(validateViewportState(validViewport)).toBe(true);
    });

    it('should validate viewport with extreme values', () => {
      const validViewport: ViewportState = {
        target: [1000, -1000, 500],
        rotationOrbit: 720,
        rotationX: 360,
        zoom: 100
      };

      expect(validateViewportState(validViewport)).toBe(true);
    });
  });

  describe('createDefaultViewport', () => {
    it('should create default viewport with correct values', () => {
      const defaultViewport = createDefaultViewport();

      expect(defaultViewport.target).toEqual([0, 0, 0]);
      expect(defaultViewport.rotationOrbit).toBe(0);
      expect(defaultViewport.rotationX).toBe(30);
      expect(defaultViewport.zoom).toBe(0);
      expect(defaultViewport.minZoom).toBe(-10);
      expect(defaultViewport.maxZoom).toBe(10);
      expect(defaultViewport.minRotationX).toBe(-90);
      expect(defaultViewport.maxRotationX).toBe(90);
    });

    it('should create valid viewport state', () => {
      const defaultViewport = createDefaultViewport();
      expect(validateViewportState(defaultViewport)).toBe(true);
    });

    it('should create new object each time', () => {
      const viewport1 = createDefaultViewport();
      const viewport2 = createDefaultViewport();

      expect(viewport1).not.toBe(viewport2);
      expect(viewport1).toEqual(viewport2);
    });
  });
});
