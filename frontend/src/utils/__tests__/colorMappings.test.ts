/**
 * Tests for color mapping utilities
 */

import { describe, it, expect } from 'vitest';
import { 
  getPointColor, 
  getAvailableColorSchemes,
  CREATURE_COLORS,
  RARITY_COLORS 
} from '../colorMappings';

describe('colorMappings', () => {
  describe('getPointColor', () => {
    it('should return creature type color when colorScheme is creature_type', () => {
      const metadata = { creature_type: 'Sparkly_Unicorn' };
      const color = getPointColor(metadata, 'creature_type');
      expect(color).toEqual(CREATURE_COLORS['Sparkly_Unicorn']);
    });

    it('should return default creature color for unknown creature type', () => {
      const metadata = { creature_type: 'Unknown_Creature' };
      const color = getPointColor(metadata, 'creature_type');
      expect(color).toEqual(CREATURE_COLORS.default);
    });

    it('should return default creature color when creature_type is missing', () => {
      const metadata = {};
      const color = getPointColor(metadata, 'creature_type');
      expect(color).toEqual(CREATURE_COLORS.default);
    });

    it('should return rarity color when colorScheme is rarity', () => {
      const metadata = { rarity: 'legendary' };
      const color = getPointColor(metadata, 'rarity');
      expect(color).toEqual(RARITY_COLORS.legendary);
    });

    it('should return default rarity color for unknown rarity', () => {
      const metadata = { rarity: 'unknown' };
      const color = getPointColor(metadata, 'rarity');
      expect(color).toEqual(RARITY_COLORS.default);
    });

    it('should return magic level gradient color when colorScheme is magic_level', () => {
      const metadata = { magic_level: 10 };
      const color = getPointColor(metadata, 'magic_level');
      expect(color).toEqual([255, 0, 0]); // Full red for max magic level
    });

    it('should handle magic level 0', () => {
      const metadata = { magic_level: 0 };
      const color = getPointColor(metadata, 'magic_level');
      expect(color).toEqual([0, 0, 255]); // Full blue for min magic level
    });

    it('should handle magic level 5 (middle)', () => {
      const metadata = { magic_level: 5 };
      const color = getPointColor(metadata, 'magic_level');
      expect(color).toEqual([127, 0, 128]); // Middle gradient
    });

    it('should use default magic level when missing', () => {
      const metadata = {};
      const color = getPointColor(metadata, 'magic_level');
      expect(color).toEqual([127, 0, 128]); // Default magic level 5
    });

    it('should return default blue color for default scheme', () => {
      const metadata = {};
      const color = getPointColor(metadata, 'default');
      expect(color).toEqual([100, 150, 255]);
    });

    it('should cap magic level intensity at 255', () => {
      const metadata = { magic_level: 20 }; // Above max
      const color = getPointColor(metadata, 'magic_level');
      expect(color[0]).toBe(255); // Should be capped at 255
    });
  });

  describe('getAvailableColorSchemes', () => {
    it('should return all available color schemes', () => {
      const schemes = getAvailableColorSchemes();
      
      expect(schemes).toHaveLength(4);
      expect(schemes).toContainEqual({ value: 'creature_type', label: 'Creature Type' });
      expect(schemes).toContainEqual({ value: 'rarity', label: 'Rarity' });
      expect(schemes).toContainEqual({ value: 'magic_level', label: 'Magic Level' });
      expect(schemes).toContainEqual({ value: 'default', label: 'Default' });
    });

    it('should return schemes in correct order', () => {
      const schemes = getAvailableColorSchemes();
      
      expect(schemes[0].value).toBe('creature_type');
      expect(schemes[1].value).toBe('rarity');
      expect(schemes[2].value).toBe('magic_level');
      expect(schemes[3].value).toBe('default');
    });
  });

  describe('color constants', () => {
    it('should have valid RGB values in CREATURE_COLORS', () => {
      Object.values(CREATURE_COLORS).forEach(color => {
        expect(color).toHaveLength(3);
        color.forEach(value => {
          expect(value).toBeGreaterThanOrEqual(0);
          expect(value).toBeLessThanOrEqual(255);
          expect(Number.isInteger(value)).toBe(true);
        });
      });
    });

    it('should have valid RGB values in RARITY_COLORS', () => {
      Object.values(RARITY_COLORS).forEach(color => {
        expect(color).toHaveLength(3);
        color.forEach(value => {
          expect(value).toBeGreaterThanOrEqual(0);
          expect(value).toBeLessThanOrEqual(255);
          expect(Number.isInteger(value)).toBe(true);
        });
      });
    });

    it('should have default colors defined', () => {
      expect(CREATURE_COLORS.default).toBeDefined();
      expect(RARITY_COLORS.default).toBeDefined();
    });
  });
});
