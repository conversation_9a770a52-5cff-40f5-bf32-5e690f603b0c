/**
 * Tests for formatter utilities
 */

import { describe, it, expect } from 'vitest';
import { 
  formatDate, 
  formatFileSize, 
  formatNumber, 
  formatCoordinate, 
  formatLabel 
} from '../formatters';

describe('formatters', () => {
  describe('formatDate', () => {
    it('should format timestamp to readable date', () => {
      const timestamp = new Date('2023-12-25').getTime();
      const formatted = formatDate(timestamp);
      expect(formatted).toMatch(/12\/25\/2023|25\/12\/2023|2023-12-25/); // Different locales
    });

    it('should handle current date', () => {
      const now = Date.now();
      const formatted = formatDate(now);
      expect(formatted).toBeTruthy();
      expect(typeof formatted).toBe('string');
    });
  });

  describe('formatFileSize', () => {
    it('should return N/A for undefined bytes', () => {
      expect(formatFileSize()).toBe('N/A');
      expect(formatFileSize(undefined)).toBe('N/A');
    });

    it('should format bytes correctly', () => {
      expect(formatFileSize(0)).toBe('0 B');
      expect(formatFileSize(512)).toBe('512 B');
      expect(formatFileSize(1023)).toBe('1023 B');
    });

    it('should format kilobytes correctly', () => {
      expect(formatFileSize(1024)).toBe('1.0 KB');
      expect(formatFileSize(1536)).toBe('1.5 KB');
      expect(formatFileSize(2048)).toBe('2.0 KB');
    });

    it('should format megabytes correctly', () => {
      expect(formatFileSize(1024 * 1024)).toBe('1.0 MB');
      expect(formatFileSize(1.5 * 1024 * 1024)).toBe('1.5 MB');
    });

    it('should format gigabytes correctly', () => {
      expect(formatFileSize(1024 * 1024 * 1024)).toBe('1.0 GB');
      expect(formatFileSize(2.5 * 1024 * 1024 * 1024)).toBe('2.5 GB');
    });

    it('should format terabytes correctly', () => {
      expect(formatFileSize(1024 * 1024 * 1024 * 1024)).toBe('1.0 TB');
    });
  });

  describe('formatNumber', () => {
    it('should format numbers with thousands separators', () => {
      expect(formatNumber(1000)).toBe('1,000');
      expect(formatNumber(1234567)).toBe('1,234,567');
      expect(formatNumber(999)).toBe('999');
    });

    it('should handle zero and negative numbers', () => {
      expect(formatNumber(0)).toBe('0');
      expect(formatNumber(-1000)).toBe('-1,000');
    });

    it('should handle decimal numbers', () => {
      expect(formatNumber(1000.5)).toBe('1,000.5');
    });
  });

  describe('formatCoordinate', () => {
    it('should format coordinates with default 2 decimal places', () => {
      expect(formatCoordinate(1.23456)).toBe('1.23');
      expect(formatCoordinate(-2.98765)).toBe('-2.99');
      expect(formatCoordinate(0)).toBe('0.00');
    });

    it('should format coordinates with custom decimal places', () => {
      expect(formatCoordinate(1.23456, 0)).toBe('1');
      expect(formatCoordinate(1.23456, 1)).toBe('1.2');
      expect(formatCoordinate(1.23456, 3)).toBe('1.235');
      expect(formatCoordinate(1.23456, 4)).toBe('1.2346');
    });

    it('should handle edge cases', () => {
      expect(formatCoordinate(0, 0)).toBe('0');
      expect(formatCoordinate(-0, 2)).toBe('0.00');
      expect(formatCoordinate(Infinity, 2)).toBe('Infinity');
    });
  });

  describe('formatLabel', () => {
    it('should replace underscores with spaces', () => {
      expect(formatLabel('creature_type')).toBe('Creature Type');
      expect(formatLabel('magic_level')).toBe('Magic Level');
      expect(formatLabel('some_long_label_name')).toBe('Some Long Label Name');
    });

    it('should capitalize first letter of each word', () => {
      expect(formatLabel('hello')).toBe('Hello');
      expect(formatLabel('hello_world')).toBe('Hello World');
      expect(formatLabel('a_b_c')).toBe('A B C');
    });

    it('should handle edge cases', () => {
      expect(formatLabel('')).toBe('');
      expect(formatLabel('_')).toBe(' ');
      expect(formatLabel('__')).toBe('  ');
      expect(formatLabel('a')).toBe('A');
    });

    it('should handle mixed case input', () => {
      expect(formatLabel('camelCase')).toBe('CamelCase');
      expect(formatLabel('UPPERCASE')).toBe('UPPERCASE');
      expect(formatLabel('mixedCase_with_underscores')).toBe('MixedCase With Underscores');
    });

    it('should handle numbers and special characters', () => {
      expect(formatLabel('level_1')).toBe('Level 1');
      expect(formatLabel('type_2_creature')).toBe('Type 2 Creature');
    });
  });
});
