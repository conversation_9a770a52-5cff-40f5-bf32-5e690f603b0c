/**
 * Viewport calculation utilities for 3D visualization
 */

import { ViewportState, EmbeddingTypeStats } from '../types/api';

/**
 * Calculate optimal viewport state based on data bounds
 */
export function calculateOptimalViewport(
  embeddingStats: EmbeddingTypeStats
): Partial<ViewportState> {
  if (!embeddingStats.bounds.x || !embeddingStats.bounds.y) {
    return {
      longitude: 0,
      latitude: 0,
      zoom: 0,
      pitch: 45,
      bearing: 0
    };
  }

  const [minX, maxX] = embeddingStats.bounds.x;
  const [minY, maxY] = embeddingStats.bounds.y;
  
  const centerX = (minX + maxX) / 2;
  const centerY = (minY + maxY) / 2;
  const range = Math.max(maxX - minX, maxY - minY);
  
  // Calculate zoom level based on data range
  // Larger ranges need smaller zoom values
  const zoom = Math.max(-5, Math.min(5, Math.log2(100 / range)));
  
  return {
    longitude: centerX,
    latitude: centerY,
    zoom,
    pitch: 45,
    bearing: 0
  };
}

/**
 * Validate viewport state values
 */
export function validateViewportState(viewState: ViewportState): boolean {
  return (
    typeof viewState.longitude === 'number' &&
    typeof viewState.latitude === 'number' &&
    typeof viewState.zoom === 'number' &&
    typeof viewState.pitch === 'number' &&
    typeof viewState.bearing === 'number' &&
    !isNaN(viewState.longitude) &&
    !isNaN(viewState.latitude) &&
    !isNaN(viewState.zoom) &&
    !isNaN(viewState.pitch) &&
    !isNaN(viewState.bearing)
  );
}

/**
 * Create default viewport state
 */
export function createDefaultViewport(): ViewportState {
  return {
    longitude: 0,
    latitude: 0,
    zoom: 0,
    pitch: 45,
    bearing: 0
  };
}
