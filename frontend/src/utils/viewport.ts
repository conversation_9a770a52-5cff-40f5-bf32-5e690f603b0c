/**
 * Viewport calculation utilities for 3D visualization
 */

import { ViewportState, EmbeddingTypeStats } from '../types/api';

/**
 * Calculate optimal viewport state based on data bounds
 */
export function calculateOptimalViewport(
  embeddingStats: EmbeddingTypeStats
): Partial<ViewportState> {
  if (!embeddingStats.bounds.x || !embeddingStats.bounds.y) {
    return {
      target: [0, 0, 0],
      rotationOrbit: 0,
      rotationX: 30,
      zoom: 0
    };
  }

  const [minX, maxX] = embeddingStats.bounds.x;
  const [minY, maxY] = embeddingStats.bounds.y;

  const centerX = (minX + maxX) / 2;
  const centerY = (minY + maxY) / 2;
  const centerZ = 0; // Default Z center
  const range = Math.max(maxX - minX, maxY - minY);

  // Calculate zoom level based on data range
  // Larger ranges need smaller zoom values
  const zoom = Math.max(-5, Math.min(5, Math.log2(100 / range)));

  return {
    target: [centerX, centerY, centerZ],
    rotationOrbit: 0,
    rotationX: 30, // Nice 3D perspective
    zoom
  };
}

/**
 * Validate viewport state values
 */
export function validateViewportState(viewState: ViewportState): boolean {
  return (
    Array.isArray(viewState.target) &&
    viewState.target.length === 3 &&
    viewState.target.every(coord => typeof coord === 'number' && !isNaN(coord)) &&
    typeof viewState.rotationOrbit === 'number' &&
    typeof viewState.rotationX === 'number' &&
    typeof viewState.zoom === 'number' &&
    !isNaN(viewState.rotationOrbit) &&
    !isNaN(viewState.rotationX) &&
    !isNaN(viewState.zoom)
  );
}

/**
 * Create default viewport state
 */
export function createDefaultViewport(): ViewportState {
  return {
    target: [0, 0, 0],
    rotationOrbit: 0,
    rotationX: 30,
    zoom: 0,
    minZoom: -10,
    maxZoom: 10,
    minRotationX: -90,
    maxRotationX: 90
  };
}
