import React, { useState } from 'react';
import DatasetSelector from './components/DatasetSelector';
import EmbeddingViewer3D from './components/EmbeddingViewer3D';
import { Dataset } from './types/api';
import './App.css';

function App() {
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const handleDatasetSelect = (dataset: Dataset) => {
    setSelectedDataset(dataset);
  };

  return (
    <div className="h-screen flex bg-gray-100">
      {/* Sidebar */}
      <div className={`
        bg-white shadow-lg transition-all duration-300 ease-in-out
        ${sidebarOpen ? 'w-80' : 'w-0'}
        overflow-hidden
      `}>
        <div className="h-full overflow-y-auto">
          <div className="p-4 border-b">
            <h1 className="text-xl font-bold text-gray-900">Loom</h1>
            <p className="text-sm text-gray-600">3D Embedding Visualization</p>
          </div>

          <DatasetSelector
            selectedDatasetId={selectedDataset?.id}
            onDatasetSelect={handleDatasetSelect}
          />
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white shadow-sm border-b px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-md hover:bg-gray-100 transition-colors"
              aria-label="Toggle sidebar"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            {selectedDataset && (
              <div>
                <h2 className="font-semibold text-gray-900">{selectedDataset.name}</h2>
                <p className="text-sm text-gray-500">
                  {selectedDataset.total_points.toLocaleString()} points
                </p>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <div className="text-sm text-gray-500">
              Status:
              <span className={`ml-1 px-2 py-1 rounded-full text-xs ${
                selectedDataset?.status === 'ready'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {selectedDataset?.status || 'No dataset'}
              </span>
            </div>
          </div>
        </div>

        {/* Visualization area */}
        <div className="flex-1 relative">
          {selectedDataset ? (
            <EmbeddingViewer3D
              datasetId={selectedDataset.id}
              className="w-full h-full"
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-500">
                <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <h3 className="text-lg font-medium mb-2">No Dataset Selected</h3>
                <p className="text-sm">Choose a dataset from the sidebar to begin visualization</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
