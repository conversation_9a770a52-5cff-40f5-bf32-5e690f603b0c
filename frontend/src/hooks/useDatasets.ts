/**
 * Custom hook for managing dataset data
 */

import { useState, useEffect } from 'react';
import ApiService from '../services/api';
import { Dataset, DatasetListResponse } from '../types/api';

export interface UseDatasetsResult {
  datasets: Dataset[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * Hook for fetching and managing datasets list
 */
export function useDatasets(): UseDatasetsResult {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDatasets = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response: DatasetListResponse = await ApiService.getDatasets();
      setDatasets(response.datasets);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load datasets');
      console.error('Error loading datasets:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDatasets();
  }, []);

  return {
    datasets,
    loading,
    error,
    refetch: fetchDatasets
  };
}

export interface UseDatasetResult {
  dataset: Dataset | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * Hook for fetching a specific dataset
 */
export function useDataset(datasetId: number | null): UseDatasetResult {
  const [dataset, setDataset] = useState<Dataset | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDataset = async () => {
    if (!datasetId) {
      setDataset(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await ApiService.getDataset(datasetId);
      setDataset(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dataset');
      console.error('Error loading dataset:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDataset();
  }, [datasetId]);

  return {
    dataset,
    loading,
    error,
    refetch: fetchDataset
  };
}
