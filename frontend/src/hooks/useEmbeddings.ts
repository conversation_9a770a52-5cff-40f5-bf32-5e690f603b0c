/**
 * Custom hook for managing embedding data
 */

import { useState, useEffect } from 'react';
import ApiService from '../services/api';
import { EmbeddingPoint3D, DatasetInfoResponse } from '../types/api';

export interface UseEmbeddingsResult {
  embeddings: EmbeddingPoint3D[];
  datasetInfo: DatasetInfoResponse | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * Hook for fetching embeddings and dataset info
 */
export function useEmbeddings(
  datasetId: number | null,
  embeddingType: string = 'umap_3d'
): UseEmbeddingsResult {
  const [embeddings, setEmbeddings] = useState<EmbeddingPoint3D[]>([]);
  const [datasetInfo, setDatasetInfo] = useState<DatasetInfoResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    if (!datasetId) {
      setEmbeddings([]);
      setDatasetInfo(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Load embeddings and dataset info in parallel
      const [embeddingsResponse, infoResponse] = await Promise.all([
        ApiService.getEmbeddings3D(datasetId, embeddingType),
        ApiService.getDatasetInfo(datasetId)
      ]);

      setEmbeddings(embeddingsResponse);
      setDatasetInfo(infoResponse);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load embeddings');
      console.error('Error loading embeddings:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [datasetId, embeddingType]);

  return {
    embeddings,
    datasetInfo,
    loading,
    error,
    refetch: fetchData
  };
}

export interface UseEmbeddingTypesResult {
  embeddingTypes: string[];
  loading: boolean;
  error: string | null;
}

/**
 * Hook for getting available embedding types for a dataset
 */
export function useEmbeddingTypes(datasetId: number | null): UseEmbeddingTypesResult {
  const [embeddingTypes, setEmbeddingTypes] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEmbeddingTypes = async () => {
      if (!datasetId) {
        setEmbeddingTypes([]);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const response = await ApiService.getDatasetInfo(datasetId);
        const types = response.embedding_types.map(et => et.type);
        setEmbeddingTypes(types);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load embedding types');
        console.error('Error loading embedding types:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchEmbeddingTypes();
  }, [datasetId]);

  return {
    embeddingTypes,
    loading,
    error
  };
}
