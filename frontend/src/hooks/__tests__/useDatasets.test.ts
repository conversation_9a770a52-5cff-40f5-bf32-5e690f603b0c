/**
 * Tests for useDatasets hooks
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useDatasets, useDataset } from '../useDatasets';
import ApiService from '../../services/api';
import { mockDataset } from '../../test/utils';

// Mock the API service
vi.mock('../../services/api');
const mockApiService = vi.mocked(ApiService);

describe('useDatasets', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('useDatasets hook', () => {
    it('should fetch datasets on mount', async () => {
      const mockResponse = {
        datasets: [mockDataset],
        total: 1,
        page: 1,
        per_page: 10
      };

      mockApiService.getDatasets.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useDatasets());

      // Initially loading
      expect(result.current.loading).toBe(true);
      expect(result.current.datasets).toEqual([]);
      expect(result.current.error).toBe(null);

      // Wait for the API call to complete
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.datasets).toEqual([mockDataset]);
      expect(result.current.error).toBe(null);
      expect(mockApiService.getDatasets).toHaveBeenCalledTimes(1);
    });

    it('should handle empty datasets response', async () => {
      const mockResponse = {
        datasets: [],
        total: 0,
        page: 1,
        per_page: 10
      };

      mockApiService.getDatasets.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useDatasets());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.datasets).toEqual([]);
      expect(result.current.error).toBe(null);
    });

    it('should handle API errors', async () => {
      const errorMessage = 'Failed to fetch datasets';
      mockApiService.getDatasets.mockRejectedValueOnce(new Error(errorMessage));

      const { result } = renderHook(() => useDatasets());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.datasets).toEqual([]);
      expect(result.current.error).toBe(errorMessage);
    });

    it('should handle non-Error rejections', async () => {
      mockApiService.getDatasets.mockRejectedValueOnce('String error');

      const { result } = renderHook(() => useDatasets());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.error).toBe('Failed to load datasets');
    });

    it('should provide refetch functionality', async () => {
      const mockResponse = {
        datasets: [mockDataset],
        total: 1,
        page: 1,
        per_page: 10
      };

      mockApiService.getDatasets.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useDatasets());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Call refetch
      result.current.refetch();

      expect(mockApiService.getDatasets).toHaveBeenCalledTimes(2);
    });

    it('should reset error state on refetch', async () => {
      // First call fails
      mockApiService.getDatasets.mockRejectedValueOnce(new Error('Network error'));

      const { result } = renderHook(() => useDatasets());

      await waitFor(() => {
        expect(result.current.error).toBe('Network error');
      });

      // Second call succeeds
      const mockResponse = {
        datasets: [mockDataset],
        total: 1,
        page: 1,
        per_page: 10
      };
      mockApiService.getDatasets.mockResolvedValueOnce(mockResponse);

      result.current.refetch();

      await waitFor(() => {
        expect(result.current.error).toBe(null);
        expect(result.current.datasets).toEqual([mockDataset]);
      });
    });
  });

  describe('useDataset hook', () => {
    it('should fetch dataset when datasetId is provided', async () => {
      mockApiService.getDataset.mockResolvedValueOnce(mockDataset);

      const { result } = renderHook(() => useDataset(1));

      expect(result.current.loading).toBe(true);
      expect(result.current.dataset).toBe(null);

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.dataset).toEqual(mockDataset);
      expect(result.current.error).toBe(null);
      expect(mockApiService.getDataset).toHaveBeenCalledWith(1);
    });

    it('should not fetch when datasetId is null', () => {
      const { result } = renderHook(() => useDataset(null));

      expect(result.current.loading).toBe(false);
      expect(result.current.dataset).toBe(null);
      expect(result.current.error).toBe(null);
      expect(mockApiService.getDataset).not.toHaveBeenCalled();
    });

    it('should handle API errors', async () => {
      const errorMessage = 'Dataset not found';
      mockApiService.getDataset.mockRejectedValueOnce(new Error(errorMessage));

      const { result } = renderHook(() => useDataset(1));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.dataset).toBe(null);
      expect(result.current.error).toBe(errorMessage);
    });

    it('should refetch when datasetId changes', async () => {
      mockApiService.getDataset.mockResolvedValue(mockDataset);

      const { result, rerender } = renderHook(
        ({ datasetId }) => useDataset(datasetId),
        { initialProps: { datasetId: 1 } }
      );

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(mockApiService.getDataset).toHaveBeenCalledWith(1);

      // Change datasetId
      rerender({ datasetId: 2 });

      await waitFor(() => {
        expect(mockApiService.getDataset).toHaveBeenCalledWith(2);
      });

      expect(mockApiService.getDataset).toHaveBeenCalledTimes(2);
    });

    it('should clear dataset when datasetId becomes null', async () => {
      mockApiService.getDataset.mockResolvedValueOnce(mockDataset);

      const { result, rerender } = renderHook(
        ({ datasetId }) => useDataset(datasetId),
        { initialProps: { datasetId: 1 } }
      );

      await waitFor(() => {
        expect(result.current.dataset).toEqual(mockDataset);
      });

      // Change to null
      rerender({ datasetId: null });

      expect(result.current.dataset).toBe(null);
      expect(result.current.loading).toBe(false);
    });

    it('should provide refetch functionality', async () => {
      mockApiService.getDataset.mockResolvedValue(mockDataset);

      const { result } = renderHook(() => useDataset(1));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      result.current.refetch();

      expect(mockApiService.getDataset).toHaveBeenCalledTimes(2);
    });
  });
});
