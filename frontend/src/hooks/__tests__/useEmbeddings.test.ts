/**
 * Tests for useEmbeddings hooks
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useEmbeddings, useEmbeddingTypes } from '../useEmbeddings';
import ApiService from '../../services/api';
import { mockEmbeddingPoint, mockDatasetInfo } from '../../test/utils';

// Mock the API service
vi.mock('../../services/api');
const mockApiService = vi.mocked(ApiService);

describe('useEmbeddings hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('useEmbeddings hook', () => {
    it('should fetch embeddings and dataset info on mount', async () => {
      const mockEmbeddings = [mockEmbeddingPoint];
      
      mockApiService.getEmbeddings3D.mockResolvedValueOnce(mockEmbeddings);
      mockApiService.getDatasetInfo.mockResolvedValueOnce(mockDatasetInfo);

      const { result } = renderHook(() => useEmbeddings(1, 'umap_3d'));

      expect(result.current.loading).toBe(true);
      expect(result.current.embeddings).toEqual([]);
      expect(result.current.datasetInfo).toBe(null);

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.embeddings).toEqual(mockEmbeddings);
      expect(result.current.datasetInfo).toEqual(mockDatasetInfo);
      expect(result.current.error).toBe(null);
      
      expect(mockApiService.getEmbeddings3D).toHaveBeenCalledWith(1, 'umap_3d');
      expect(mockApiService.getDatasetInfo).toHaveBeenCalledWith(1);
    });

    it('should use default embedding type when not specified', async () => {
      const mockEmbeddings = [mockEmbeddingPoint];
      
      mockApiService.getEmbeddings3D.mockResolvedValueOnce(mockEmbeddings);
      mockApiService.getDatasetInfo.mockResolvedValueOnce(mockDatasetInfo);

      renderHook(() => useEmbeddings(1));

      await waitFor(() => {
        expect(mockApiService.getEmbeddings3D).toHaveBeenCalledWith(1, 'umap_3d');
      });
    });

    it('should not fetch when datasetId is null', () => {
      const { result } = renderHook(() => useEmbeddings(null));

      expect(result.current.loading).toBe(false);
      expect(result.current.embeddings).toEqual([]);
      expect(result.current.datasetInfo).toBe(null);
      expect(mockApiService.getEmbeddings3D).not.toHaveBeenCalled();
      expect(mockApiService.getDatasetInfo).not.toHaveBeenCalled();
    });

    it('should handle API errors', async () => {
      const errorMessage = 'Failed to load embeddings';
      mockApiService.getEmbeddings3D.mockRejectedValueOnce(new Error(errorMessage));
      mockApiService.getDatasetInfo.mockRejectedValueOnce(new Error(errorMessage));

      const { result } = renderHook(() => useEmbeddings(1));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.embeddings).toEqual([]);
      expect(result.current.datasetInfo).toBe(null);
      expect(result.current.error).toBe(errorMessage);
    });

    it('should handle partial API failures', async () => {
      const mockEmbeddings = [mockEmbeddingPoint];
      
      mockApiService.getEmbeddings3D.mockResolvedValueOnce(mockEmbeddings);
      mockApiService.getDatasetInfo.mockRejectedValueOnce(new Error('Info failed'));

      const { result } = renderHook(() => useEmbeddings(1));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.error).toBe('Info failed');
    });

    it('should refetch when datasetId changes', async () => {
      const mockEmbeddings = [mockEmbeddingPoint];
      
      mockApiService.getEmbeddings3D.mockResolvedValue(mockEmbeddings);
      mockApiService.getDatasetInfo.mockResolvedValue(mockDatasetInfo);

      const { rerender } = renderHook(
        ({ datasetId, embeddingType }) => useEmbeddings(datasetId, embeddingType),
        { initialProps: { datasetId: 1, embeddingType: 'umap_3d' } }
      );

      await waitFor(() => {
        expect(mockApiService.getEmbeddings3D).toHaveBeenCalledWith(1, 'umap_3d');
      });

      // Change datasetId
      rerender({ datasetId: 2, embeddingType: 'umap_3d' });

      await waitFor(() => {
        expect(mockApiService.getEmbeddings3D).toHaveBeenCalledWith(2, 'umap_3d');
      });

      expect(mockApiService.getEmbeddings3D).toHaveBeenCalledTimes(2);
    });

    it('should refetch when embedding type changes', async () => {
      const mockEmbeddings = [mockEmbeddingPoint];
      
      mockApiService.getEmbeddings3D.mockResolvedValue(mockEmbeddings);
      mockApiService.getDatasetInfo.mockResolvedValue(mockDatasetInfo);

      const { rerender } = renderHook(
        ({ datasetId, embeddingType }) => useEmbeddings(datasetId, embeddingType),
        { initialProps: { datasetId: 1, embeddingType: 'umap_3d' } }
      );

      await waitFor(() => {
        expect(mockApiService.getEmbeddings3D).toHaveBeenCalledWith(1, 'umap_3d');
      });

      // Change embedding type
      rerender({ datasetId: 1, embeddingType: 'original' });

      await waitFor(() => {
        expect(mockApiService.getEmbeddings3D).toHaveBeenCalledWith(1, 'original');
      });

      expect(mockApiService.getEmbeddings3D).toHaveBeenCalledTimes(2);
    });

    it('should provide refetch functionality', async () => {
      const mockEmbeddings = [mockEmbeddingPoint];
      
      mockApiService.getEmbeddings3D.mockResolvedValue(mockEmbeddings);
      mockApiService.getDatasetInfo.mockResolvedValue(mockDatasetInfo);

      const { result } = renderHook(() => useEmbeddings(1));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      result.current.refetch();

      expect(mockApiService.getEmbeddings3D).toHaveBeenCalledTimes(2);
      expect(mockApiService.getDatasetInfo).toHaveBeenCalledTimes(2);
    });

    it('should handle empty embeddings', async () => {
      mockApiService.getEmbeddings3D.mockResolvedValueOnce([]);
      mockApiService.getDatasetInfo.mockResolvedValueOnce(mockDatasetInfo);

      const { result } = renderHook(() => useEmbeddings(1));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.embeddings).toEqual([]);
      expect(result.current.error).toBe(null);
    });
  });

  describe('useEmbeddingTypes hook', () => {
    it('should fetch embedding types on mount', async () => {
      mockApiService.getDatasetInfo.mockResolvedValueOnce(mockDatasetInfo);

      const { result } = renderHook(() => useEmbeddingTypes(1));

      expect(result.current.loading).toBe(true);
      expect(result.current.embeddingTypes).toEqual([]);

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const expectedTypes = mockDatasetInfo.embedding_types.map(et => et.type);
      expect(result.current.embeddingTypes).toEqual(expectedTypes);
      expect(result.current.error).toBe(null);
    });

    it('should not fetch when datasetId is null', () => {
      const { result } = renderHook(() => useEmbeddingTypes(null));

      expect(result.current.loading).toBe(false);
      expect(result.current.embeddingTypes).toEqual([]);
      expect(result.current.error).toBe(null);
      expect(mockApiService.getDatasetInfo).not.toHaveBeenCalled();
    });

    it('should handle API errors', async () => {
      const errorMessage = 'Failed to load embedding types';
      mockApiService.getDatasetInfo.mockRejectedValueOnce(new Error(errorMessage));

      const { result } = renderHook(() => useEmbeddingTypes(1));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.embeddingTypes).toEqual([]);
      expect(result.current.error).toBe(errorMessage);
    });

    it('should handle dataset with no embedding types', async () => {
      const infoWithoutTypes = {
        ...mockDatasetInfo,
        embedding_types: []
      };
      
      mockApiService.getDatasetInfo.mockResolvedValueOnce(infoWithoutTypes);

      const { result } = renderHook(() => useEmbeddingTypes(1));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.embeddingTypes).toEqual([]);
      expect(result.current.error).toBe(null);
    });

    it('should refetch when datasetId changes', async () => {
      mockApiService.getDatasetInfo.mockResolvedValue(mockDatasetInfo);

      const { rerender } = renderHook(
        ({ datasetId }) => useEmbeddingTypes(datasetId),
        { initialProps: { datasetId: 1 } }
      );

      await waitFor(() => {
        expect(mockApiService.getDatasetInfo).toHaveBeenCalledWith(1);
      });

      // Change datasetId
      rerender({ datasetId: 2 });

      await waitFor(() => {
        expect(mockApiService.getDatasetInfo).toHaveBeenCalledWith(2);
      });

      expect(mockApiService.getDatasetInfo).toHaveBeenCalledTimes(2);
    });

    it('should clear types when datasetId becomes null', async () => {
      mockApiService.getDatasetInfo.mockResolvedValueOnce(mockDatasetInfo);

      const { result, rerender } = renderHook(
        ({ datasetId }) => useEmbeddingTypes(datasetId),
        { initialProps: { datasetId: 1 } }
      );

      await waitFor(() => {
        expect(result.current.embeddingTypes.length).toBeGreaterThan(0);
      });

      // Change to null
      rerender({ datasetId: null });

      expect(result.current.embeddingTypes).toEqual([]);
    });
  });
});
