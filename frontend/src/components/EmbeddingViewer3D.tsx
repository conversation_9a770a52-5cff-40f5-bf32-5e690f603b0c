/**
 * Main 3D embedding visualization component using Deck.gl
 */

import React, { useState, useEffect, useCallback } from 'react';
import DeckGL from '@deck.gl/react';
import { ScatterplotLayer } from '@deck.gl/layers';
import { OrbitView } from '@deck.gl/core';
import ApiService from '../services/api';
import {
  EmbeddingPoint3D,
  Dataset,
  VisualizationConfig,
  ViewportState,
  ColorMapping,
  DatasetInfoResponse
} from '../types/api';

interface EmbeddingViewer3DProps {
  datasetId: number;
  className?: string;
}

// Color mappings for different creature types
const CREATURE_COLORS: ColorMapping = {
  'Sparkly_Unicorn': [255, 192, 203],    // Pink
  'Dancing_Dragon': [255, 69, 0],        // Red-Orange
  'Mystical_Phoenix': [255, 140, 0],     // Dark Orange
  'Giggling_Goblin': [50, 205, 50],      // <PERSON>e Green
  'Wise_Owl': [139, 69, 19],             // Saddle Brown
  'Bouncing_Butterfly': [255, 20, 147],  // Deep Pink
  'Sleepy_Bear': [139, 90, 43],          // Saddle Brown
  'Curious_Cat': [128, 0, 128],          // Purple
  'Majestic_Eagle': [25, 25, 112],       // Midnight Blue
  'Playful_Dolphin': [0, 191, 255],      // Deep Sky Blue
  'default': [128, 128, 128]             // Gray
};

const RARITY_COLORS: ColorMapping = {
  'common': [169, 169, 169],     // Dark Gray
  'uncommon': [50, 205, 50],     // Lime Green
  'rare': [0, 191, 255],         // Deep Sky Blue
  'legendary': [255, 215, 0],    // Gold
  'default': [128, 128, 128]     // Gray
};

const EmbeddingViewer3D: React.FC<EmbeddingViewer3DProps> = ({ 
  datasetId, 
  className = '' 
}) => {
  const [embeddings, setEmbeddings] = useState<EmbeddingPoint3D[]>([]);
  const [dataset, setDataset] = useState<Dataset | null>(null);
  const [datasetInfo, setDatasetInfo] = useState<DatasetInfoResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hoveredPoint, setHoveredPoint] = useState<EmbeddingPoint3D | null>(null);
  
  const [config, setConfig] = useState<VisualizationConfig>({
    pointSize: 5,
    colorBy: 'creature_type',
    showLabels: false,
    embeddingType: 'umap_3d'
  });

  const [viewState, setViewState] = useState<ViewportState>({
    target: [0, 0, 0],
    rotationOrbit: 0,
    rotationX: 30,
    zoom: 0,
    minZoom: -10,
    maxZoom: 10,
    minRotationX: -90,
    maxRotationX: 90
  });

  // Load dataset and embeddings
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load dataset info and embeddings in parallel
        const [datasetResponse, embeddingsResponse, infoResponse] = await Promise.all([
          ApiService.getDataset(datasetId),
          ApiService.getEmbeddings3D(datasetId, config.embeddingType),
          ApiService.getDatasetInfo(datasetId)
        ]);

        setDataset(datasetResponse);
        setEmbeddings(embeddingsResponse);
        setDatasetInfo(infoResponse);

        // Set initial viewport based on data bounds
        if (infoResponse.embedding_types.length > 0) {
          const embeddingStats = infoResponse.embedding_types.find(
            et => et.type === config.embeddingType
          );

          if (embeddingStats?.bounds.x && embeddingStats?.bounds.y) {
            const [minX, maxX] = embeddingStats.bounds.x;
            const [minY, maxY] = embeddingStats.bounds.y;
            const centerX = (minX + maxX) / 2;
            const centerY = (minY + maxY) / 2;
            const centerZ = 0; // Default Z center
            const range = Math.max(maxX - minX, maxY - minY);

            setViewState({
              target: [centerX, centerY, centerZ],
              rotationOrbit: 0,
              rotationX: 30, // Nice 3D perspective
              zoom: Math.log2(100 / range), // Adjust zoom based on data range
              minZoom: -10,
              maxZoom: 10,
              minRotationX: -90,
              maxRotationX: 90
            });
          }
        }

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data');
        console.error('Error loading data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [datasetId, config.embeddingType]);

  // Get color for a point based on current color scheme
  const getPointColor = useCallback((point: EmbeddingPoint3D): [number, number, number] => {
    switch (config.colorBy) {
      case 'creature_type':
        const creatureType = point.metadata?.creature_type || 'default';
        return CREATURE_COLORS[creatureType] || CREATURE_COLORS.default;
      
      case 'rarity':
        const rarity = point.metadata?.rarity || 'default';
        return RARITY_COLORS[rarity] || RARITY_COLORS.default;
      
      case 'magic_level':
        const magicLevel = point.metadata?.magic_level || 5;
        const intensity = Math.min(255, (magicLevel / 10) * 255);
        return [intensity, 0, 255 - intensity]; // Blue to Red gradient
      
      default:
        return [100, 150, 255]; // Default blue
    }
  }, [config.colorBy]);

  // Create Deck.gl layer
  const layers = [
    new ScatterplotLayer({
      id: 'embeddings-3d',
      data: embeddings,
      getPosition: (d: EmbeddingPoint3D) => [d.x, d.y, d.z || 0],
      getRadius: (d: EmbeddingPoint3D) => config.pointSize,
      getFillColor: getPointColor,
      getLineColor: [255, 255, 255],
      getLineWidth: 1,
      lineWidthMinPixels: 0.5,
      radiusMinPixels: 2,
      radiusMaxPixels: 50,
      pickable: true,
      onHover: (info) => {
        setHoveredPoint(info.object || null);
      },
      updateTriggers: {
        getFillColor: [config.colorBy],
        getRadius: [config.pointSize]
      }
    })
  ];

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading embeddings...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center text-red-600">
          <p className="text-lg font-semibold mb-2">Error Loading Data</p>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative h-full ${className}`}>
      {/* Main visualization */}
      <DeckGL
        views={new OrbitView()}
        viewState={viewState}
        onViewStateChange={({ viewState }) => setViewState(viewState)}
        layers={layers}
        controller={true}
        style={{ position: 'relative', width: '100%', height: '100%' }}
      />

      {/* Dataset info overlay */}
      <div className="absolute top-4 left-4 bg-white bg-opacity-90 rounded-lg p-4 shadow-lg max-w-sm">
        <h3 className="font-bold text-lg mb-2">{dataset?.name}</h3>
        <p className="text-sm text-gray-600 mb-2">{dataset?.description}</p>
        <div className="text-sm">
          <p><strong>Points:</strong> {embeddings.length.toLocaleString()}</p>
          <p><strong>Type:</strong> {config.embeddingType}</p>
          <p><strong>Color by:</strong> {config.colorBy.replace('_', ' ')}</p>
        </div>
      </div>

      {/* Hover tooltip */}
      {hoveredPoint && (
        <div className="absolute top-4 right-4 bg-black bg-opacity-80 text-white rounded-lg p-3 shadow-lg max-w-xs">
          <h4 className="font-semibold mb-1">{hoveredPoint.label}</h4>
          <div className="text-sm space-y-1">
            <p><strong>Position:</strong> ({hoveredPoint.x.toFixed(2)}, {hoveredPoint.y.toFixed(2)}, {hoveredPoint.z?.toFixed(2)})</p>
            {hoveredPoint.metadata?.creature_type && (
              <p><strong>Type:</strong> {hoveredPoint.metadata.creature_type.replace('_', ' ')}</p>
            )}
            {hoveredPoint.metadata?.rarity && (
              <p><strong>Rarity:</strong> {hoveredPoint.metadata.rarity}</p>
            )}
            {hoveredPoint.metadata?.magic_level && (
              <p><strong>Magic Level:</strong> {hoveredPoint.metadata.magic_level}/10</p>
            )}
          </div>
        </div>
      )}

      {/* Controls overlay */}
      <div className="absolute bottom-4 left-4 bg-white bg-opacity-90 rounded-lg p-4 shadow-lg">
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium mb-1">
              Point Size: {config.pointSize}
            </label>
            <input
              type="range"
              min="1"
              max="30"
              step="1"
              value={config.pointSize}
              onChange={(e) => {
                const newSize = parseInt(e.target.value);
                console.log('Point size changed to:', newSize);
                setConfig(prev => ({ ...prev, pointSize: newSize }));
              }}
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Color By</label>
            <select
              value={config.colorBy}
              onChange={(e) => setConfig(prev => ({ ...prev, colorBy: e.target.value as any }))}
              className="w-full p-1 border rounded text-sm"
            >
              <option value="creature_type">Creature Type</option>
              <option value="rarity">Rarity</option>
              <option value="magic_level">Magic Level</option>
              <option value="default">Default</option>
            </select>
          </div>

          {datasetInfo && datasetInfo.embedding_types.length > 1 && (
            <div>
              <label className="block text-sm font-medium mb-1">Embedding Type</label>
              <select
                value={config.embeddingType}
                onChange={(e) => setConfig(prev => ({ ...prev, embeddingType: e.target.value }))}
                className="w-full p-1 border rounded text-sm"
              >
                {datasetInfo.embedding_types.map(et => (
                  <option key={et.type} value={et.type}>
                    {et.type} ({et.count} points)
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmbeddingViewer3D;
