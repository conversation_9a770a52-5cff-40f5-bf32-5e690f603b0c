/**
 * Hover tooltip component for 3D points
 */

import React from 'react';
import { EmbeddingPoint3D } from '../../types/api';
import { formatCoordinate, formatLabel } from '../../utils/formatters';

interface HoverTooltipProps {
  point: EmbeddingPoint3D;
  className?: string;
}

const HoverTooltip: React.FC<HoverTooltipProps> = ({ point, className = '' }) => {
  return (
    <div 
      className={`bg-black bg-opacity-80 text-white rounded-lg p-3 shadow-lg max-w-xs ${className}`}
      data-testid="hover-tooltip"
    >
      <h4 className="font-semibold mb-1" data-testid="tooltip-label">
        {point.label || `Point ${point.id}`}
      </h4>
      
      <div className="text-sm space-y-1">
        <div data-testid="tooltip-position">
          <strong>Position:</strong> (
          {formatCoordinate(point.x)}, {formatCoordinate(point.y)}, {formatCoordinate(point.z || 0)}
          )
        </div>
        
        {point.metadata?.creature_type && (
          <div data-testid="tooltip-creature-type">
            <strong>Type:</strong> {formatLabel(point.metadata.creature_type)}
          </div>
        )}
        
        {point.metadata?.rarity && (
          <div data-testid="tooltip-rarity">
            <strong>Rarity:</strong> {formatLabel(point.metadata.rarity)}
          </div>
        )}
        
        {point.metadata?.magic_level !== undefined && (
          <div data-testid="tooltip-magic-level">
            <strong>Magic Level:</strong> {point.metadata.magic_level}/10
          </div>
        )}
        
        {point.metadata?.habitat && (
          <div data-testid="tooltip-habitat">
            <strong>Habitat:</strong> {formatLabel(point.metadata.habitat)}
          </div>
        )}
        
        {point.original_index !== undefined && (
          <div data-testid="tooltip-index">
            <strong>Index:</strong> {point.original_index}
          </div>
        )}
      </div>
    </div>
  );
};

export default HoverTooltip;
