import React from 'react';

export interface SamplingConfig {
  sampleSize: number;
  samplingMethod: 'random' | 'systematic' | 'first_n';
  seed?: number;
}

interface SamplingControlsProps {
  totalPoints: number;
  currentSampleSize: number;
  samplingConfig: SamplingConfig;
  onSamplingConfigChange: (config: SamplingConfig) => void;
  onResample: () => void;
  isLoading?: boolean;
  className?: string;
}

const SAMPLE_SIZE_PRESETS = [
  { label: '1K', value: 1000 },
  { label: '10K', value: 10000 },
  { label: '50K', value: 50000 },
  { label: '100K', value: 100000 },
  { label: '500K', value: 500000 },
  { label: '1M', value: 1000000 }
];

const SAMPLING_METHODS = [
  { value: 'random', label: 'Random', description: 'Statistically representative sample' },
  { value: 'systematic', label: 'Systematic', description: 'Every Nth point' },
  { value: 'first_n', label: 'First N', description: 'First N points (fastest)' }
] as const;

function getOptimalSampleSize(totalPoints: number): number {
  if (totalPoints <= 10000) return totalPoints;      // Show all
  if (totalPoints <= 50000) return Math.min(25000, totalPoints);  // 50% sample
  if (totalPoints <= 500000) return Math.min(50000, totalPoints); // 10% sample
  return 100000;                                      // Fixed 100K for large datasets
}

export default function SamplingControls({
  totalPoints,
  currentSampleSize,
  samplingConfig,
  onSamplingConfigChange,
  onResample,
  isLoading = false,
  className = ''
}: SamplingControlsProps) {
  const isSubsampled = currentSampleSize < totalPoints;
  const samplingRatio = totalPoints > 0 ? (currentSampleSize / totalPoints) * 100 : 100;

  const handleSampleSizeChange = (newSize: number) => {
    const clampedSize = Math.min(newSize, totalPoints);
    onSamplingConfigChange({
      ...samplingConfig,
      sampleSize: clampedSize
    });
  };

  const handleMethodChange = (method: SamplingConfig['samplingMethod']) => {
    onSamplingConfigChange({
      ...samplingConfig,
      samplingMethod: method
    });
  };

  const handleOptimalSize = () => {
    const optimalSize = getOptimalSampleSize(totalPoints);
    handleSampleSizeChange(optimalSize);
  };

  const selectedMethod = SAMPLING_METHODS.find(m => m.value === samplingConfig.samplingMethod);

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-900">Dataset Sampling</h3>
        {isSubsampled && (
          <button
            onClick={onResample}
            disabled={isLoading}
            className="text-xs bg-blue-50 text-blue-600 px-2 py-1 rounded hover:bg-blue-100 disabled:opacity-50"
          >
            {isLoading ? 'Loading...' : 'Resample'}
          </button>
        )}
      </div>

      {/* Sampling Status */}
      <div className="text-sm text-gray-600">
        <div className="flex justify-between items-center">
          <span>Showing:</span>
          <span className="font-medium">
            {currentSampleSize.toLocaleString()} of {totalPoints.toLocaleString()} points
          </span>
        </div>
        {isSubsampled && (
          <div className="flex justify-between items-center mt-1">
            <span>Sample ratio:</span>
            <span className="font-medium">{samplingRatio.toFixed(1)}%</span>
          </div>
        )}
      </div>

      {/* Sample Size Control */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium text-gray-700">
            Sample Size
          </label>
          <button
            onClick={handleOptimalSize}
            className="text-xs text-blue-600 hover:text-blue-800"
          >
            Use Optimal
          </button>
        </div>
        
        {/* Preset Buttons */}
        <div className="flex flex-wrap gap-1 mb-2">
          {SAMPLE_SIZE_PRESETS.map(preset => (
            <button
              key={preset.value}
              onClick={() => handleSampleSizeChange(preset.value)}
              disabled={preset.value > totalPoints}
              className={`text-xs px-2 py-1 rounded border ${
                samplingConfig.sampleSize === preset.value
                  ? 'bg-blue-100 border-blue-300 text-blue-700'
                  : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
              } disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              {preset.label}
            </button>
          ))}
        </div>

        {/* Custom Size Slider */}
        <div className="space-y-2">
          <input
            type="range"
            min="1000"
            max={totalPoints}
            step="1000"
            value={samplingConfig.sampleSize}
            onChange={(e) => handleSampleSizeChange(parseInt(e.target.value))}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>1K</span>
            <span>{totalPoints.toLocaleString()}</span>
          </div>
        </div>
      </div>

      {/* Sampling Method */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Sampling Method
        </label>
        <div className="space-y-2">
          {SAMPLING_METHODS.map(method => (
            <label key={method.value} className="flex items-start space-x-2 cursor-pointer">
              <input
                type="radio"
                name="samplingMethod"
                value={method.value}
                checked={samplingConfig.samplingMethod === method.value}
                onChange={() => handleMethodChange(method.value)}
                className="mt-0.5"
              />
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-900">{method.label}</div>
                <div className="text-xs text-gray-500">{method.description}</div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Advanced Options */}
      {samplingConfig.samplingMethod === 'random' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Random Seed (optional)
          </label>
          <input
            type="number"
            value={samplingConfig.seed || ''}
            onChange={(e) => onSamplingConfigChange({
              ...samplingConfig,
              seed: e.target.value ? parseInt(e.target.value) : undefined
            })}
            placeholder="Leave empty for random"
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
          <div className="text-xs text-gray-500 mt-1">
            Set a seed for reproducible sampling
          </div>
        </div>
      )}

      {/* Performance Warning */}
      {samplingConfig.sampleSize > 500000 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded p-2">
          <div className="text-xs text-yellow-800">
            ⚠️ Large sample sizes may impact performance
          </div>
        </div>
      )}
    </div>
  );
}

export { getOptimalSampleSize };
