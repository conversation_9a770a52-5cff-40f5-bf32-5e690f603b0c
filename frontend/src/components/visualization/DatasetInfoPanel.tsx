/**
 * Dataset information panel component
 */

import React from 'react';
import { Dataset, DatasetInfoResponse } from '../../types/api';
import { formatNumber } from '../../utils/formatters';

interface DatasetInfoPanelProps {
  dataset: Dataset;
  datasetInfo?: DatasetInfoResponse | null;
  embeddingCount: number;
  embeddingType: string;
  className?: string;
}

const DatasetInfoPanel: React.FC<DatasetInfoPanelProps> = ({
  dataset,
  datasetInfo,
  embeddingCount,
  embeddingType,
  className = ''
}) => {
  return (
    <div className={`bg-white bg-opacity-90 rounded-lg p-4 shadow-lg max-w-sm ${className}`}>
      <h3 className="font-bold text-lg mb-2" data-testid="dataset-name">
        {dataset.name}
      </h3>
      
      {dataset.description && (
        <p className="text-sm text-gray-600 mb-2" data-testid="dataset-description">
          {dataset.description}
        </p>
      )}
      
      <div className="text-sm space-y-1">
        <div data-testid="dataset-points">
          <strong>Points:</strong> {formatNumber(embeddingCount)}
        </div>
        
        <div data-testid="dataset-embedding-type">
          <strong>Type:</strong> {embeddingType}
        </div>
        
        {datasetInfo && (
          <div data-testid="dataset-total-points">
            <strong>Total Points:</strong> {formatNumber(dataset.total_points)}
          </div>
        )}
        
        {dataset.embedding_dimension && (
          <div data-testid="dataset-dimensions">
            <strong>Dimensions:</strong> {dataset.embedding_dimension}
          </div>
        )}
      </div>
      
      {datasetInfo && datasetInfo.embedding_types.length > 1 && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <h4 className="text-sm font-medium mb-2">Available Types:</h4>
          <div className="space-y-1 text-xs">
            {datasetInfo.embedding_types.map(et => (
              <div key={et.type} className="flex justify-between">
                <span>{et.type}</span>
                <span className="text-gray-500">{formatNumber(et.count)}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DatasetInfoPanel;
