/**
 * Tests for VisualizationControls component
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '../../../test/utils';
import VisualizationControls from '../VisualizationControls';
import { mockVisualizationConfig, mockDatasetInfo } from '../../../test/utils';

describe('VisualizationControls', () => {
  const mockOnConfigChange = vi.fn();

  beforeEach(() => {
    mockOnConfigChange.mockClear();
  });

  it('should render all control elements', () => {
    render(
      <VisualizationControls 
        config={mockVisualizationConfig} 
        onConfigChange={mockOnConfigChange} 
      />
    );
    
    expect(screen.getByTestId('point-size-control')).toBeInTheDocument();
    expect(screen.getByTestId('color-scheme-control')).toBeInTheDocument();
    expect(screen.getByTestId('labels-control')).toBeInTheDocument();
    expect(screen.getByText('Visualization Controls')).toBeInTheDocument();
  });

  it('should display current point size value', () => {
    const config = { ...mockVisualizationConfig, pointSize: 15 };
    render(
      <VisualizationControls 
        config={config} 
        onConfigChange={mockOnConfigChange} 
      />
    );
    
    expect(screen.getByText('Point Size: 15')).toBeInTheDocument();
  });

  it('should handle point size slider changes', () => {
    render(
      <VisualizationControls 
        config={mockVisualizationConfig} 
        onConfigChange={mockOnConfigChange} 
      />
    );
    
    const slider = screen.getByTestId('point-size-slider');
    fireEvent.change(slider, { target: { value: '20' } });
    
    expect(mockOnConfigChange).toHaveBeenCalledWith({
      ...mockVisualizationConfig,
      pointSize: 20
    });
  });

  it('should handle color scheme changes', () => {
    render(
      <VisualizationControls 
        config={mockVisualizationConfig} 
        onConfigChange={mockOnConfigChange} 
      />
    );
    
    const select = screen.getByTestId('color-scheme-select');
    fireEvent.change(select, { target: { value: 'rarity' } });
    
    expect(mockOnConfigChange).toHaveBeenCalledWith({
      ...mockVisualizationConfig,
      colorBy: 'rarity'
    });
  });

  it('should handle labels toggle', () => {
    render(
      <VisualizationControls 
        config={mockVisualizationConfig} 
        onConfigChange={mockOnConfigChange} 
      />
    );
    
    const checkbox = screen.getByTestId('labels-checkbox');
    fireEvent.click(checkbox);
    
    expect(mockOnConfigChange).toHaveBeenCalledWith({
      ...mockVisualizationConfig,
      showLabels: true
    });
  });

  it('should show embedding type control when multiple types available', () => {
    render(
      <VisualizationControls 
        config={mockVisualizationConfig} 
        onConfigChange={mockOnConfigChange}
        datasetInfo={mockDatasetInfo}
      />
    );
    
    expect(screen.getByTestId('embedding-type-control')).toBeInTheDocument();
    expect(screen.getByTestId('embedding-type-select')).toBeInTheDocument();
  });

  it('should not show embedding type control when only one type available', () => {
    const singleTypeInfo = {
      ...mockDatasetInfo,
      embedding_types: [mockDatasetInfo.embedding_types[0]]
    };
    
    render(
      <VisualizationControls 
        config={mockVisualizationConfig} 
        onConfigChange={mockOnConfigChange}
        datasetInfo={singleTypeInfo}
      />
    );
    
    expect(screen.queryByTestId('embedding-type-control')).not.toBeInTheDocument();
  });

  it('should handle embedding type changes', () => {
    render(
      <VisualizationControls 
        config={mockVisualizationConfig} 
        onConfigChange={mockOnConfigChange}
        datasetInfo={mockDatasetInfo}
      />
    );
    
    const select = screen.getByTestId('embedding-type-select');
    fireEvent.change(select, { target: { value: 'original' } });
    
    expect(mockOnConfigChange).toHaveBeenCalledWith({
      ...mockVisualizationConfig,
      embeddingType: 'original'
    });
  });

  it('should display embedding type options with counts', () => {
    render(
      <VisualizationControls 
        config={mockVisualizationConfig} 
        onConfigChange={mockOnConfigChange}
        datasetInfo={mockDatasetInfo}
      />
    );
    
    expect(screen.getByText('umap_3d (1000 points)')).toBeInTheDocument();
    expect(screen.getByText('original (1000 points)')).toBeInTheDocument();
  });

  it('should have correct slider attributes', () => {
    render(
      <VisualizationControls 
        config={mockVisualizationConfig} 
        onConfigChange={mockOnConfigChange} 
      />
    );
    
    const slider = screen.getByTestId('point-size-slider');
    expect(slider).toHaveAttribute('type', 'range');
    expect(slider).toHaveAttribute('min', '1');
    expect(slider).toHaveAttribute('max', '30');
    expect(slider).toHaveAttribute('step', '1');
    expect(slider).toHaveAttribute('value', '5');
  });

  it('should display all color scheme options', () => {
    render(
      <VisualizationControls 
        config={mockVisualizationConfig} 
        onConfigChange={mockOnConfigChange} 
      />
    );
    
    expect(screen.getByText('Creature Type')).toBeInTheDocument();
    expect(screen.getByText('Rarity')).toBeInTheDocument();
    expect(screen.getByText('Magic Level')).toBeInTheDocument();
    expect(screen.getByText('Default')).toBeInTheDocument();
  });

  it('should apply custom className', () => {
    const customClass = 'custom-controls-class';
    render(
      <VisualizationControls 
        config={mockVisualizationConfig} 
        onConfigChange={mockOnConfigChange}
        className={customClass}
      />
    );
    
    const container = screen.getByText('Visualization Controls').parentElement;
    expect(container).toHaveClass(customClass);
  });

  it('should reflect current config values in form elements', () => {
    const config = {
      pointSize: 12,
      colorBy: 'rarity' as const,
      showLabels: true,
      embeddingType: 'original'
    };
    
    render(
      <VisualizationControls 
        config={config} 
        onConfigChange={mockOnConfigChange}
        datasetInfo={mockDatasetInfo}
      />
    );
    
    expect(screen.getByTestId('point-size-slider')).toHaveAttribute('value', '12');
    expect(screen.getByTestId('color-scheme-select')).toHaveValue('rarity');
    expect(screen.getByTestId('labels-checkbox')).toBeChecked();
    expect(screen.getByTestId('embedding-type-select')).toHaveValue('original');
  });
});
