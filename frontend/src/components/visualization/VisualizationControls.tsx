/**
 * Controls panel for 3D visualization
 */

import React from 'react';
import { VisualizationConfig, DatasetInfoResponse } from '../../types/api';
import { getAvailableColorSchemes, ColorScheme } from '../../utils/colorMappings';

interface VisualizationControlsProps {
  config: VisualizationConfig;
  onConfigChange: (config: VisualizationConfig) => void;
  datasetInfo?: DatasetInfoResponse | null;
  className?: string;
}

const VisualizationControls: React.FC<VisualizationControlsProps> = ({
  config,
  onConfigChange,
  datasetInfo,
  className = ''
}) => {
  const colorSchemes = getAvailableColorSchemes();

  const handlePointSizeChange = (size: number) => {
    onConfigChange({ ...config, pointSize: size });
  };

  const handleColorSchemeChange = (colorBy: ColorScheme) => {
    onConfigChange({ ...config, colorBy });
  };

  const handleEmbeddingTypeChange = (embeddingType: string) => {
    onConfigChange({ ...config, embeddingType });
  };

  const handleLabelsToggle = (showLabels: boolean) => {
    onConfigChange({ ...config, showLabels });
  };

  return (
    <div className={`bg-white bg-opacity-90 rounded-lg p-4 shadow-lg ${className}`}>
      <h3 className="font-semibold mb-3 text-gray-900">Visualization Controls</h3>
      
      <div className="space-y-3">
        {/* Point Size Control */}
        <div data-testid="point-size-control">
          <label className="block text-sm font-medium mb-1">
            Point Size: {(config.pointSize * 100).toFixed(0)}%
          </label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={config.pointSize}
            onChange={(e) => handlePointSizeChange(parseFloat(e.target.value))}
            className="w-full"
            data-testid="point-size-slider"
          />
        </div>
        
        {/* Color Scheme Control */}
        <div data-testid="color-scheme-control">
          <label className="block text-sm font-medium mb-1">Color By</label>
          <select
            value={config.colorBy}
            onChange={(e) => handleColorSchemeChange(e.target.value as ColorScheme)}
            className="w-full p-1 border rounded text-sm"
            data-testid="color-scheme-select"
          >
            {colorSchemes.map(scheme => (
              <option key={scheme.value} value={scheme.value}>
                {scheme.label}
              </option>
            ))}
          </select>
        </div>

        {/* Embedding Type Control */}
        {datasetInfo && datasetInfo.embedding_types.length > 1 && (
          <div data-testid="embedding-type-control">
            <label className="block text-sm font-medium mb-1">Embedding Type</label>
            <select
              value={config.embeddingType}
              onChange={(e) => handleEmbeddingTypeChange(e.target.value)}
              className="w-full p-1 border rounded text-sm"
              data-testid="embedding-type-select"
            >
              {datasetInfo.embedding_types.map(et => (
                <option key={et.type} value={et.type}>
                  {et.type} ({et.count} points)
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Labels Toggle */}
        <div data-testid="labels-control">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.showLabels}
              onChange={(e) => handleLabelsToggle(e.target.checked)}
              className="rounded"
              data-testid="labels-checkbox"
            />
            <span className="text-sm font-medium">Show Labels</span>
          </label>
        </div>
      </div>
    </div>
  );
};

export default VisualizationControls;
