/**
 * Dataset selector component for choosing which dataset to visualize
 */

import React, { useState, useEffect } from 'react';
import ApiService from '../services/api';
import { Dataset, DatasetListResponse } from '../types/api';

interface DatasetSelectorProps {
  selectedDatasetId?: number;
  onDatasetSelect: (dataset: Dataset) => void;
  className?: string;
}

const DatasetSelector: React.FC<DatasetSelectorProps> = ({
  selectedDatasetId,
  onDatasetSelect,
  className = ''
}) => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadDatasets = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response: DatasetListResponse = await ApiService.getDatasets();
        setDatasets(response.datasets);
        
        // Auto-select first dataset if none selected
        if (!selectedDatasetId && response.datasets.length > 0) {
          onDatasetSelect(response.datasets[0]);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load datasets');
        console.error('Error loading datasets:', err);
      } finally {
        setLoading(false);
      }
    };

    loadDatasets();
  }, [selectedDatasetId, onDatasetSelect]);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'N/A';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  if (loading) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="text-red-600 text-center">
          <p className="font-semibold mb-2">Error Loading Datasets</p>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (datasets.length === 0) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="text-gray-500 text-center">
          <p className="font-semibold mb-2">No Datasets Found</p>
          <p className="text-sm">No datasets are available for visualization.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-4 ${className}`}>
      <h2 className="text-lg font-bold mb-4">Select Dataset</h2>
      
      <div className="space-y-3">
        {datasets.map((dataset) => (
          <div
            key={dataset.id}
            onClick={() => onDatasetSelect(dataset)}
            className={`
              p-4 border rounded-lg cursor-pointer transition-all duration-200
              ${selectedDatasetId === dataset.id 
                ? 'border-blue-500 bg-blue-50 shadow-md' 
                : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
              }
            `}
          >
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-semibold text-gray-900">{dataset.name}</h3>
              <span className={`
                px-2 py-1 text-xs rounded-full
                ${dataset.status === 'ready' 
                  ? 'bg-green-100 text-green-800' 
                  : dataset.status === 'processing'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-red-100 text-red-800'
                }
              `}>
                {dataset.status}
              </span>
            </div>
            
            {dataset.description && (
              <p className="text-sm text-gray-600 mb-3">{dataset.description}</p>
            )}
            
            <div className="grid grid-cols-2 gap-4 text-sm text-gray-500">
              <div>
                <span className="font-medium">Points:</span> {dataset.total_points.toLocaleString()}
              </div>
              <div>
                <span className="font-medium">Dimensions:</span> {dataset.embedding_dimension || 'N/A'}
              </div>
              <div>
                <span className="font-medium">Created:</span> {formatDate(dataset.created_at)}
              </div>
              <div>
                <span className="font-medium">Size:</span> {formatFileSize(dataset.file_size)}
              </div>
            </div>
            
            {dataset.error_message && (
              <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                <strong>Error:</strong> {dataset.error_message}
              </div>
            )}
          </div>
        ))}
      </div>
      
      <div className="mt-4 text-sm text-gray-500 text-center">
        {datasets.length} dataset{datasets.length !== 1 ? 's' : ''} available
      </div>
    </div>
  );
};

export default DatasetSelector;
