/**
 * Reusable error message component
 */

import React from 'react';

interface ErrorMessageProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  className?: string;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  title = 'Error',
  message,
  onRetry,
  className = ''
}) => {
  return (
    <div className={`text-center text-red-600 ${className}`} data-testid="error-message">
      <div className="mb-4">
        <svg 
          className="w-12 h-12 mx-auto mb-2 text-red-400" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
          data-testid="error-icon"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={1} 
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" 
          />
        </svg>
      </div>
      
      <h3 className="text-lg font-semibold mb-2" data-testid="error-title">
        {title}
      </h3>
      
      <p className="text-sm mb-4" data-testid="error-description">
        {message}
      </p>
      
      {onRetry && (
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          data-testid="retry-button"
        >
          Try Again
        </button>
      )}
    </div>
  );
};

export default ErrorMessage;
