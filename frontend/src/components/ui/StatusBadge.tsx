/**
 * Reusable status badge component
 */

import React from 'react';

interface StatusBadgeProps {
  status: string;
  className?: string;
}

const statusStyles: Record<string, string> = {
  ready: 'bg-green-100 text-green-800',
  processing: 'bg-yellow-100 text-yellow-800',
  error: 'bg-red-100 text-red-800',
  pending: 'bg-gray-100 text-gray-800',
  default: 'bg-gray-100 text-gray-800'
};

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className = '' }) => {
  const statusClass = statusStyles[status] || statusStyles.default;
  
  return (
    <span 
      className={`px-2 py-1 text-xs rounded-full ${statusClass} ${className}`}
      data-testid="status-badge"
      data-status={status}
    >
      {status}
    </span>
  );
};

export default StatusBadge;
