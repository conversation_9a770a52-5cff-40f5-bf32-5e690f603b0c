/**
 * Tests for LoadingSpinner component
 */

import { describe, it, expect } from 'vitest';
import { render, screen } from '../../../test/utils';
import LoadingSpinner from '../LoadingSpinner';

describe('LoadingSpinner', () => {
  it('should render spinner with default props', () => {
    render(<LoadingSpinner />);
    
    const spinner = screen.getByTestId('loading-spinner');
    expect(spinner).toBeInTheDocument();
    expect(spinner).toHaveClass('h-12', 'w-12'); // Default medium size
  });

  it('should render with custom message', () => {
    const message = 'Loading data...';
    render(<LoadingSpinner message={message} />);
    
    const messageElement = screen.getByTestId('loading-message');
    expect(messageElement).toBeInTheDocument();
    expect(messageElement).toHaveTextContent(message);
  });

  it('should not render message when not provided', () => {
    render(<LoadingSpinner />);
    
    const messageElement = screen.queryByTestId('loading-message');
    expect(messageElement).not.toBeInTheDocument();
  });

  it('should render small size spinner', () => {
    render(<LoadingSpinner size="sm" />);
    
    const spinner = screen.getByTestId('loading-spinner');
    expect(spinner).toHaveClass('h-6', 'w-6');
  });

  it('should render large size spinner', () => {
    render(<LoadingSpinner size="lg" />);
    
    const spinner = screen.getByTestId('loading-spinner');
    expect(spinner).toHaveClass('h-16', 'w-16');
  });

  it('should apply custom className', () => {
    const customClass = 'custom-spinner-class';
    render(<LoadingSpinner className={customClass} />);
    
    const container = screen.getByTestId('loading-spinner').parentElement?.parentElement;
    expect(container).toHaveClass(customClass);
  });

  it('should have spinning animation', () => {
    render(<LoadingSpinner />);
    
    const spinner = screen.getByTestId('loading-spinner');
    expect(spinner).toHaveClass('animate-spin');
  });

  it('should have proper accessibility attributes', () => {
    render(<LoadingSpinner message="Loading..." />);
    
    const spinner = screen.getByTestId('loading-spinner');
    expect(spinner).toBeInTheDocument();
    
    const message = screen.getByTestId('loading-message');
    expect(message).toBeInTheDocument();
  });

  it('should render with all size options', () => {
    const { rerender } = render(<LoadingSpinner size="sm" />);
    expect(screen.getByTestId('loading-spinner')).toHaveClass('h-6', 'w-6');

    rerender(<LoadingSpinner size="md" />);
    expect(screen.getByTestId('loading-spinner')).toHaveClass('h-12', 'w-12');

    rerender(<LoadingSpinner size="lg" />);
    expect(screen.getByTestId('loading-spinner')).toHaveClass('h-16', 'w-16');
  });
});
