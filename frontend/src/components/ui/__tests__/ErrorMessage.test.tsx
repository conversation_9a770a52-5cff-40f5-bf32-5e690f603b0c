/**
 * Tests for ErrorMessage component
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '../../../test/utils';
import ErrorMessage from '../ErrorMessage';

describe('ErrorMessage', () => {
  it('should render with default title and message', () => {
    const message = 'Something went wrong';
    render(<ErrorMessage message={message} />);
    
    expect(screen.getByTestId('error-message')).toBeInTheDocument();
    expect(screen.getByTestId('error-title')).toHaveTextContent('Error');
    expect(screen.getByTestId('error-description')).toHaveTextContent(message);
    expect(screen.getByTestId('error-icon')).toBeInTheDocument();
  });

  it('should render with custom title', () => {
    const title = 'Custom Error Title';
    const message = 'Custom error message';
    render(<ErrorMessage title={title} message={message} />);
    
    expect(screen.getByTestId('error-title')).toHaveTextContent(title);
  });

  it('should render retry button when onRetry is provided', () => {
    const message = 'Network error';
    const onRetry = vi.fn();
    render(<ErrorMessage message={message} onRetry={onRetry} />);
    
    const retryButton = screen.getByTestId('retry-button');
    expect(retryButton).toBeInTheDocument();
    expect(retryButton).toHaveTextContent('Try Again');
  });

  it('should not render retry button when onRetry is not provided', () => {
    const message = 'Error without retry';
    render(<ErrorMessage message={message} />);
    
    const retryButton = screen.queryByTestId('retry-button');
    expect(retryButton).not.toBeInTheDocument();
  });

  it('should call onRetry when retry button is clicked', () => {
    const message = 'Retryable error';
    const onRetry = vi.fn();
    render(<ErrorMessage message={message} onRetry={onRetry} />);
    
    const retryButton = screen.getByTestId('retry-button');
    fireEvent.click(retryButton);
    
    expect(onRetry).toHaveBeenCalledTimes(1);
  });

  it('should apply custom className', () => {
    const message = 'Test error';
    const customClass = 'custom-error-class';
    render(<ErrorMessage message={message} className={customClass} />);
    
    const errorContainer = screen.getByTestId('error-message');
    expect(errorContainer).toHaveClass(customClass);
  });

  it('should have proper styling classes', () => {
    const message = 'Styled error';
    render(<ErrorMessage message={message} />);
    
    const errorContainer = screen.getByTestId('error-message');
    expect(errorContainer).toHaveClass('text-center', 'text-red-600');
    
    const icon = screen.getByTestId('error-icon');
    expect(icon).toHaveClass('w-12', 'h-12', 'text-red-400');
    
    const title = screen.getByTestId('error-title');
    expect(title).toHaveClass('text-lg', 'font-semibold');
    
    const description = screen.getByTestId('error-description');
    expect(description).toHaveClass('text-sm');
  });

  it('should handle long error messages', () => {
    const longMessage = 'This is a very long error message that should still be displayed properly even when it contains a lot of text and might wrap to multiple lines in the UI.';
    render(<ErrorMessage message={longMessage} />);
    
    const description = screen.getByTestId('error-description');
    expect(description).toHaveTextContent(longMessage);
  });

  it('should handle empty message gracefully', () => {
    render(<ErrorMessage message="" />);
    
    const description = screen.getByTestId('error-description');
    expect(description).toHaveTextContent('');
  });

  it('should render all elements in correct order', () => {
    const message = 'Test message';
    const onRetry = vi.fn();
    render(<ErrorMessage title="Test Title" message={message} onRetry={onRetry} />);
    
    const container = screen.getByTestId('error-message');
    const children = Array.from(container.children);
    
    // Should have icon container, title, description, and retry button
    expect(children).toHaveLength(4);
  });
});
