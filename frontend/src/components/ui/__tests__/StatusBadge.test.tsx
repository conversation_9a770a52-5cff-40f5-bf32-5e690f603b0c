/**
 * Tests for StatusBadge component
 */

import { describe, it, expect } from 'vitest';
import { render, screen } from '../../../test/utils';
import StatusBadge from '../StatusBadge';

describe('StatusBadge', () => {
  it('should render with ready status', () => {
    render(<StatusBadge status="ready" />);
    
    const badge = screen.getByTestId('status-badge');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveTextContent('ready');
    expect(badge).toHaveAttribute('data-status', 'ready');
    expect(badge).toHaveClass('bg-green-100', 'text-green-800');
  });

  it('should render with processing status', () => {
    render(<StatusBadge status="processing" />);
    
    const badge = screen.getByTestId('status-badge');
    expect(badge).toHaveTextContent('processing');
    expect(badge).toHaveAttribute('data-status', 'processing');
    expect(badge).toHaveClass('bg-yellow-100', 'text-yellow-800');
  });

  it('should render with error status', () => {
    render(<StatusBadge status="error" />);
    
    const badge = screen.getByTestId('status-badge');
    expect(badge).toHaveTextContent('error');
    expect(badge).toHaveAttribute('data-status', 'error');
    expect(badge).toHaveClass('bg-red-100', 'text-red-800');
  });

  it('should render with pending status', () => {
    render(<StatusBadge status="pending" />);
    
    const badge = screen.getByTestId('status-badge');
    expect(badge).toHaveTextContent('pending');
    expect(badge).toHaveAttribute('data-status', 'pending');
    expect(badge).toHaveClass('bg-gray-100', 'text-gray-800');
  });

  it('should render with unknown status using default styling', () => {
    render(<StatusBadge status="unknown" />);
    
    const badge = screen.getByTestId('status-badge');
    expect(badge).toHaveTextContent('unknown');
    expect(badge).toHaveAttribute('data-status', 'unknown');
    expect(badge).toHaveClass('bg-gray-100', 'text-gray-800');
  });

  it('should apply custom className', () => {
    const customClass = 'custom-badge-class';
    render(<StatusBadge status="ready" className={customClass} />);
    
    const badge = screen.getByTestId('status-badge');
    expect(badge).toHaveClass(customClass);
  });

  it('should have base styling classes', () => {
    render(<StatusBadge status="ready" />);
    
    const badge = screen.getByTestId('status-badge');
    expect(badge).toHaveClass('px-2', 'py-1', 'text-xs', 'rounded-full');
  });

  it('should handle empty status', () => {
    render(<StatusBadge status="" />);
    
    const badge = screen.getByTestId('status-badge');
    expect(badge).toHaveTextContent('');
    expect(badge).toHaveAttribute('data-status', '');
    expect(badge).toHaveClass('bg-gray-100', 'text-gray-800'); // Default styling
  });

  it('should handle status with special characters', () => {
    const specialStatus = 'status-with-dashes_and_underscores';
    render(<StatusBadge status={specialStatus} />);
    
    const badge = screen.getByTestId('status-badge');
    expect(badge).toHaveTextContent(specialStatus);
    expect(badge).toHaveAttribute('data-status', specialStatus);
  });

  it('should be accessible', () => {
    render(<StatusBadge status="ready" />);
    
    const badge = screen.getByTestId('status-badge');
    expect(badge.tagName).toBe('SPAN');
    expect(badge).toHaveAttribute('data-testid', 'status-badge');
    expect(badge).toHaveAttribute('data-status', 'ready');
  });

  it('should render all status types correctly', () => {
    const statuses = ['ready', 'processing', 'error', 'pending'];
    const expectedClasses = [
      ['bg-green-100', 'text-green-800'],
      ['bg-yellow-100', 'text-yellow-800'],
      ['bg-red-100', 'text-red-800'],
      ['bg-gray-100', 'text-gray-800']
    ];

    statuses.forEach((status, index) => {
      const { unmount } = render(<StatusBadge status={status} />);

      const badge = screen.getByTestId('status-badge');
      expect(badge).toHaveClass(...expectedClasses[index]);

      // Clean up before next iteration
      unmount();
    });
  });
});
