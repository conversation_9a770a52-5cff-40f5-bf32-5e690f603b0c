/**
 * Integration tests for component interactions
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../../test/utils';
import DatasetSelector from '../DatasetSelector';
import { mockDataset } from '../../test/utils';

// Mock the API service
vi.mock('../../services/api', () => ({
  default: {
    getDatasets: vi.fn(),
    getDataset: vi.fn(),
    getEmbeddings3D: vi.fn(),
    getDatasetInfo: vi.fn()
  }
}));

describe('Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('DatasetSelector Integration', () => {
    it('should handle dataset selection flow', async () => {
      const mockOnDatasetSelect = vi.fn();
      const mockDatasets = [mockDataset];

      // Mock successful API response
      const ApiService = await import('../../services/api');
      vi.mocked(ApiService.default.getDatasets).mockResolvedValueOnce({
        datasets: mockDatasets,
        total: 1,
        page: 1,
        per_page: 10
      });

      render(
        <DatasetSelector
          selectedDatasetId={null}
          onDatasetSelect={mockOnDatasetSelect}
        />
      );

      // Wait for datasets to load
      await waitFor(() => {
        expect(screen.getByText('Test Dataset')).toBeInTheDocument();
      });

      // Select a dataset by clicking on the dataset card
      const datasetCard = screen.getByText('Test Dataset').closest('div[class*="cursor-pointer"]');
      fireEvent.click(datasetCard!);

      // Verify callback was called with the full dataset object
      expect(mockOnDatasetSelect).toHaveBeenCalledWith(mockDataset);
    });

    it('should display loading state initially', () => {
      const mockOnDatasetSelect = vi.fn();

      render(
        <DatasetSelector
          selectedDatasetId={null}
          onDatasetSelect={mockOnDatasetSelect}
        />
      );

      // Check for the skeleton loading animation
      const loadingElement = document.querySelector('.animate-pulse');
      expect(loadingElement).toBeInTheDocument();

      // Check for skeleton placeholder elements
      const skeletonElements = document.querySelectorAll('.bg-gray-200');
      expect(skeletonElements.length).toBeGreaterThan(0);
    });

    it('should handle API errors gracefully', async () => {
      const mockOnDatasetSelect = vi.fn();

      // Mock API error
      const ApiService = await import('../../services/api');
      vi.mocked(ApiService.default.getDatasets).mockRejectedValueOnce(
        new Error('Network error')
      );

      render(
        <DatasetSelector
          selectedDatasetId={null}
          onDatasetSelect={mockOnDatasetSelect}
        />
      );

      // Wait for error to appear - look for specific error text
      await waitFor(() => {
        expect(screen.getByText('Error Loading Datasets')).toBeInTheDocument();
        expect(screen.getByText('Network error')).toBeInTheDocument();
      });
    });

    it('should show selected dataset when provided', async () => {
      const mockOnDatasetSelect = vi.fn();
      const mockDatasets = [mockDataset];

      // Mock successful API response
      const ApiService = await import('../../services/api');
      vi.mocked(ApiService.default.getDatasets).mockResolvedValueOnce({
        datasets: mockDatasets,
        total: 1,
        page: 1,
        per_page: 10
      });

      render(
        <DatasetSelector
          selectedDatasetId={1}
          onDatasetSelect={mockOnDatasetSelect}
        />
      );

      // Wait for datasets to load
      await waitFor(() => {
        expect(screen.getByText('Test Dataset')).toBeInTheDocument();
      });

      // Should show the selected dataset with highlighted styling
      const datasetCard = screen.getByText('Test Dataset').closest('div[class*="cursor-pointer"]');
      expect(datasetCard).toHaveClass('border-blue-500', 'bg-blue-50');
    });
  });

  describe('Component Error Boundaries', () => {
    it('should handle component errors gracefully', () => {
      // This would test error boundaries if we had them implemented
      // For now, just verify components don't crash with invalid props
      
      expect(() => {
        render(
          <DatasetSelector 
            selectedDatasetId={null} 
            onDatasetSelect={() => {}} 
          />
        );
      }).not.toThrow();
    });
  });
});
