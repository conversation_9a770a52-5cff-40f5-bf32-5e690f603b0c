/**
 * TypeScript types for API responses and data structures
 */

export interface Dataset {
  id: number;
  name: string;
  description?: string;
  owner_id: number;
  total_points: number;
  embedding_dimension?: number;
  file_path?: string;
  file_size?: number;
  status: string;
  error_message?: string;
  created_at: number;
  updated_at: number;
  processed_at?: number;
}

export interface DatasetListResponse {
  datasets: Dataset[];
  total: number;
  skip: number;
  limit: number;
}

export interface EmbeddingPoint3D {
  id: number;
  x: number;
  y: number;
  z: number;
  label?: string;
  metadata: Record<string, any>;
  embedding_type: string;
  dimensions: number;
  original_index?: number;
}

export interface EmbeddingResponse {
  dataset_id: number;
  embedding_type?: string;
  total_points: number;
  embeddings: EmbeddingPoint3D[];
}

export interface EmbeddingTypeStats {
  type: string;
  count: number;
  bounds: {
    x?: [number, number];
    y?: [number, number];
    z?: [number, number];
  };
}

export interface DatasetInfoResponse {
  dataset: Dataset;
  embedding_types: EmbeddingTypeStats[];
}

export interface ApiError {
  error: string;
  message: string;
  details?: Record<string, any>;
}

// Visualization-specific types
export interface VisualizationConfig {
  pointSize: number;
  colorBy: 'creature_type' | 'rarity' | 'magic_level' | 'default';
  showLabels: boolean;
  embeddingType: string;
}

export interface ViewportState {
  longitude: number;
  latitude: number;
  zoom: number;
  pitch: number;
  bearing: number;
}

export interface ColorMapping {
  [key: string]: [number, number, number]; // RGB values
}
