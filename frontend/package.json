{"name": "loom-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@deck.gl/core": "^9.1.12", "@deck.gl/layers": "^9.1.12", "@deck.gl/mesh-layers": "^9.1.12", "@deck.gl/react": "^9.1.12", "@loaders.gl/core": "^3.4.13", "@loaders.gl/json": "^3.4.13", "@luma.gl/engine": "^9.1.9", "axios": "^1.6.0", "deck.gl": "^9.1.12", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.0", "@types/jest": "^29.5.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.0", "eslint-plugin-react-hooks": "^4.6.0", "jsdom": "^22.1.0", "typescript": "^5.0.0", "vite": "^4.4.0", "vitest": "^0.34.0"}}