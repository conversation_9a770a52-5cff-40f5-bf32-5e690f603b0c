{"name": "loom-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.0", "deck.gl": "^8.9.0", "@deck.gl/react": "^8.9.0", "@deck.gl/layers": "^8.9.0", "@deck.gl/core": "^8.9.0", "@loaders.gl/core": "^3.4.13", "@loaders.gl/json": "^3.4.13"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.0", "eslint-plugin-react-hooks": "^4.6.0", "typescript": "^5.0.0", "vite": "^4.4.0"}}